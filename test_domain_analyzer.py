#!/usr/bin/env python3
"""
Test domain gap analyzer
"""
import sys
import os
from pathlib import Path

# Add scripts directory to path
sys.path.append('scripts')

def test_domain_analyzer():
    try:
        # Test imports
        print("Testing imports...")
        import torch
        print(f"✓ PyTorch available: {torch.__version__}")
        
        from sklearn.decomposition import PCA
        print("✓ scikit-learn available")
        
        # Test file paths
        annotation_file = "K:/Sim2Real_30k/annotations/instances_complete.json"
        image_dir = "K:/Sim2Real_30k/images"
        output_dir = "K:/Sim2Real_30k/domain_analysis"
        
        print(f"\nTesting file paths...")
        print(f"Annotation file exists: {Path(annotation_file).exists()}")
        print(f"Image directory exists: {Path(image_dir).exists()}")
        
        # Test domain gap analyzer import
        print(f"\nTesting domain gap analyzer import...")
        from domain_gap_analyzer import DomainGapAnalyzer
        print("✓ DomainGapAnalyzer imported successfully")
        
        # Create analyzer instance
        print(f"\nCreating analyzer instance...")
        analyzer = DomainGapAnalyzer(annotation_file, image_dir, output_dir)
        print("✓ Analyzer created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_domain_analyzer()
    if success:
        print("\n✓ All tests passed!")
    else:
        print("\n❌ Tests failed!")
