#!/usr/bin/env python3
"""
Quick dataset check script
"""
import json
import os
from pathlib import Path

def check_dataset():
    dataset_path = "K:/Sim2Real_30k"
    annotation_file = f"{dataset_path}/annotations/instances_complete.json"
    
    print(f"Checking dataset at: {dataset_path}")
    
    # Check if annotation file exists
    if not os.path.exists(annotation_file):
        print(f"❌ Annotation file not found: {annotation_file}")
        return
    
    # Load and check annotation data
    try:
        with open(annotation_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✓ Annotation file loaded successfully")
        print(f"  - Images: {len(data['images'])}")
        print(f"  - Annotations: {len(data['annotations'])}")
        print(f"  - Categories: {len(data['categories'])}")
        
        # Check images directory
        images_dir = f"{dataset_path}/images"
        if os.path.exists(images_dir):
            image_files = list(Path(images_dir).glob("*.png"))
            print(f"  - Image files found: {len(image_files)}")
        else:
            print(f"❌ Images directory not found: {images_dir}")
            
    except Exception as e:
        print(f"❌ Error loading annotation file: {e}")

if __name__ == "__main__":
    check_dataset()
