# 数据处理管道使用指南

## 概述
数据处理管道用于从扫描结果中筛选出高质量的红外小目标检测数据集，并转换为COCO格式。

## 功能特性
- ✅ 智能采样策略，确保尺寸分布合理
- ✅ 时序间隔过滤，避免相邻帧相似度过高
- ✅ 自动COCO格式转换
- ✅ 质量检查和错误处理
- ✅ 详细的处理报告和统计
- ✅ 进度监控和中间结果保存

## 使用方法

### 1. 基本用法
```bash
# 使用默认配置处理30,000张图像
python scripts/data_processor.py scan_results.json -o output

# 指定图像数量
python scripts/data_processor.py scan_results.json -o output -n 50000

# 使用自定义配置
python scripts/data_processor.py scan_results.json -o output --config config/processing_config.json
```

### 2. 配置文件
创建自定义配置文件来调整处理参数：

```json
{
  "target_count": 30000,
  "frame_interval": [5, 10],
  "size_distribution": {
    "0-4px": 0.25,
    "5-9px": 0.35,
    "10-14px": 0.20,
    "15-19px": 0.10,
    "20-24px": 0.06,
    "25-29px": 0.03,
    "30-34px": 0.01
  }
}
```

### 3. 输出结构
```
output/
├── images/                    # 筛选出的图像文件
│   ├── batch1_000001.png
│   ├── batch1_000002.png
│   └── ...
├── annotations/               # COCO格式标注
│   └── instances_train.json
├── reports/                   # 处理报告
│   ├── sampling_report.json  # 采样统计
│   └── quality_report.json   # 质量分析
└── metadata/                  # 元数据（如果启用）
    ├── batch_mapping.json
    └── size_distribution.json
```

## 配置参数说明

### 核心参数
- `target_count`: 目标图像数量
- `frame_interval`: 时序采样间隔 [最小, 最大]
- `size_distribution`: 各尺寸范围的目标分布比例

### 质量控制
- `quality_threshold`: 质量阈值 (0.0-1.0)
- `max_errors_per_batch`: 每个batch允许的最大错误数

### 输出选项
- `copy_images`: 是否复制图像文件
- `generate_reports`: 是否生成详细报告
- `validate_outputs`: 是否验证输出质量

## 处理流程

### 阶段1: 数据加载和分析
1. 加载扫描结果文件
2. 分析batch分布和质量
3. 计算每个batch的采样配额

### 阶段2: 智能采样
1. 按尺寸分布进行分层采样
2. 应用时序间隔过滤
3. 质量检查和验证

### 阶段3: 格式转换
1. 生成COCO格式标注文件
2. 复制选中的图像文件
3. 创建统一的命名规范

### 阶段4: 报告生成
1. 采样统计报告
2. 质量分析报告
3. 处理日志和错误记录

## 质量保证

### 数据验证
- ✅ 图像文件完整性检查
- ✅ JSON格式验证
- ✅ 目标边界框有效性
- ✅ 尺寸范围符合性

### 分布验证
- ✅ 尺寸分布符合目标配置
- ✅ Batch覆盖率检查
- ✅ 时序间隔统计
- ✅ 错误率监控

## 性能优化

### 内存管理
- 分批处理，避免内存溢出
- 及时释放不需要的数据
- 使用生成器减少内存占用

### 处理速度
- 并行处理多个batch
- 优化文件I/O操作
- 智能缓存机制

## 故障排除

### 常见问题
1. **内存不足**: 减少target_count或分批处理
2. **磁盘空间不足**: 检查输出目录可用空间
3. **JSON解析错误**: 检查扫描结果文件格式
4. **图像文件缺失**: 验证原始数据完整性

### 错误处理
- 自动跳过损坏的文件
- 详细的错误日志记录
- 优雅的异常恢复机制

## 示例用法

### 快速开始
```bash
# 1. 确保已完成数据扫描
python scripts/dataset_scanner.py -p "你的数据路径" -o scan_results.json

# 2. 处理数据集
python scripts/data_processor.py scan_results.json -o output -n 30000

# 3. 检查结果
ls -la output/
cat output/reports/sampling_report.json
```

### 自定义配置
```bash
# 1. 创建配置文件
cp config/processing_config.json my_config.json

# 2. 编辑配置
nano my_config.json

# 3. 使用自定义配置处理
python scripts/data_processor.py scan_results.json -o output --config my_config.json
```

### 批量处理
```bash
# 处理多个不同大小的数据集
for count in 10000 20000 30000; do
    python scripts/data_processor.py scan_results.json -o "output_${count}" -n $count
done
```

## 注意事项

1. **存储空间**: 确保输出目录有足够空间（约8MB/图像）
2. **处理时间**: 大数据集处理可能需要较长时间
3. **数据备份**: 建议在处理前备份原始数据
4. **配置验证**: 处理前验证配置参数的合理性

## 下一步
处理完成后，你可以：
1. 使用生成的COCO数据集训练模型
2. 进行数据可视化和质量检查
3. 根据需要调整采样策略
4. 扩展数据集规模
