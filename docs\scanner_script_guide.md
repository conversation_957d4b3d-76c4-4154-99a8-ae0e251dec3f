# 数据集扫描脚本使用说明

## 脚本功能
`scripts/dataset_scanner.py` 用于扫描指定路径下的所有图像-标签对，统计数据分布情况。

## 主要功能
1. **递归扫描**所有子文件夹
2. **识别配对**两种命名模式的图像-标签对
3. **目标分析**解析JSON标签并应用筛选标准
4. **统计分布**目标尺寸、数量等信息
5. **存储评估**预估筛选后的存储需求

## 使用方法

### 1. 安装依赖（推荐）
```bash
pip install tqdm  # 用于显示进度条
```

### 2. 运行方式

#### 方式1: 交互式输入路径（推荐）
```bash
python scripts/dataset_scanner.py
```
程序会提示你输入扫描路径，支持路径验证。

#### 方式2: 命令行直接指定路径
```bash
# Windows路径示例
python scripts/dataset_scanner.py -p "G:\孤立的文件\孤立的文件 45"

# Linux路径示例
python scripts/dataset_scanner.py --path /mnt/dataset/batch1

# 指定输出文件名
python scripts/dataset_scanner.py -p "D:\data" -o my_results.json

# 静默模式（减少输出）
python scripts/dataset_scanner.py -p "D:\data" --quiet
```

#### 方式3: 查看帮助
```bash
python scripts/dataset_scanner.py --help
```

### 3. 查看结果
- **控制台输出**: 实时显示扫描进度条和摘要
- **详细结果**: 保存在指定的JSON文件中（默认: `scan_results.json`）

## 输出信息

### 控制台摘要
```
扫描结果摘要
============================================================
总文件夹数: 150
有效batch数: 45
总图像-标签对: 12500
有效目标总数: 8900

目标尺寸分布:
  0-4px: 1200
  5-9px: 2800
  10-14px: 3200
  15-19px: 1100
  20-24px: 400
  25-29px: 150
  30-34px: 50

预估存储需求:
  预计筛选图像数: 7800
  预计存储空间: 60.9 GB
```

### JSON详细结果
```json
{
  "total_folders": 150,
  "valid_batches": 45,
  "total_pairs": 12500,
  "valid_targets": 8900,
  "batch_info": [
    {
      "path": "G:\\孤立的文件\\孤立的文件 45\\22080717007 总和\\data5",
      "total_pairs": 280,
      "valid_pairs": 245,
      "total_targets": 189,
      "size_distribution": {
        "5-9px": 45,
        "10-14px": 89,
        "15-19px": 35
      }
    }
  ]
}
```

## 筛选标准
脚本应用以下筛选标准：
- `m_blk_path`包含`"gameData/flightModels/"`
- `isdead: false`
- `isvisible: true`  
- 目标尺寸: 3-33像素

## 新功能特性

### 1. 智能路径输入
- **交互式验证**: 自动检查路径是否存在和有效
- **错误处理**: 路径无效时提供重新输入选项
- **格式兼容**: 自动处理引号和空格

### 2. 进度显示
- **进度条**: 使用tqdm库显示扫描进度（如果已安装）
- **实时统计**: 显示已发现的有效batch数量
- **阶段提示**: 清晰显示当前扫描阶段

### 3. 命令行支持
- **灵活输入**: 支持交互式和命令行两种方式
- **参数控制**: 可指定输出文件名和静默模式
- **帮助信息**: 内置详细的使用说明

## 注意事项
1. **依赖安装**: 建议安装tqdm获得更好的进度显示体验
2. **路径格式**: 支持Windows和Linux路径格式
3. **编码问题**: 脚本使用UTF-8编码处理中文路径
4. **内存使用**: 大量文件时可能占用较多内存
5. **运行时间**: 根据文件数量，扫描可能需要几分钟到几十分钟
6. **中断恢复**: 支持Ctrl+C安全中断扫描

## 故障排除
- **路径不存在**: 程序会自动验证并提示重新输入
- **权限问题**: 确保对目标路径有读取权限
- **JSON解析错误**: 检查标签文件格式是否正确
- **内存不足**: 可以使用静默模式减少内存占用
- **进度条异常**: 如果tqdm显示异常，可以卸载后使用简单进度显示

