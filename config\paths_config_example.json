{"dataset_root": "/path/to/your/dataset", "output_root": "/path/to/output", "temp_dir": "/path/to/temp", "platform": "auto-detect", "created_by": "paths_config_example.json", "version": "1.0", "description": "路径配置示例文件，请复制为 paths_config.json 并修改路径", "subdirectories": {"images": "images", "annotations": "annotations", "reports": "reports", "visualization": "visualization", "domain_analysis": "domain_analysis"}, "file_patterns": {"scan_results": "scan_results.json", "complete_annotations": "instances_complete.json", "train_annotations": "instances_train_split.json", "val_annotations": "instances_val_split.json"}, "examples": {"windows": {"dataset_root": "F:\\Sim2Real_30k", "output_root": "F:\\Sim2Real_30k_output", "temp_dir": "F:\\temp\\sim2real", "note": "Windows路径示例，使用双反斜杠或正斜杠"}, "linux": {"dataset_root": "/mnt/data/Sim2Real_30k", "output_root": "/mnt/output/Sim2Real_30k", "temp_dir": "/tmp/sim2real", "note": "Linux路径示例"}, "mac": {"dataset_root": "/Volumes/Data/Sim2Real_30k", "output_root": "/Users/<USER>/Documents/Sim2Real_30k_output", "temp_dir": "/tmp/sim2real", "note": "macOS路径示例"}}, "usage_instructions": ["1. 复制此文件为 paths_config.json", "2. 修改 dataset_root 为您的数据集路径", "3. 修改 output_root 为您希望的输出路径", "4. 修改 temp_dir 为临时文件路径", "5. 运行 python scripts/setup_paths.py 进行交互式配置"]}