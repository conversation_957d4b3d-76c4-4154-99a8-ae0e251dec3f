#!/bin/bash
# Sim2Real_30k 数据集网页快速部署脚本
# 服务器IP: **************

echo "🚀 开始部署 Sim2Real_30k 数据集网页..."

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请使用 sudo 运行此脚本"
    exit 1
fi

# 更新系统
echo "📦 更新系统包..."
apt update && apt upgrade -y

# 安装Nginx
echo "🌐 安装Nginx..."
apt install nginx -y

# 启动Nginx服务
echo "🔧 启动Nginx服务..."
systemctl start nginx
systemctl enable nginx

# 配置防火墙
echo "🔥 配置防火墙..."
ufw allow 80
ufw allow 443

# 清空默认网页目录
echo "🗑️ 清空默认网页目录..."
rm -rf /var/www/html/*

# 提示用户上传文件
echo ""
echo "📁 请按以下步骤上传网页文件："
echo "1. 将本地的 K:\Sim2Real_30k\final_with_batch_analysis\ 文件夹打包为 sim2real_website.zip"
echo "2. 上传到百度网盘、WeTransfer 或其他文件传输服务"
echo "3. 获取下载链接"
echo "4. 输入下载链接（按回车继续）："

read -p "请输入文件下载链接: " DOWNLOAD_URL

if [ -z "$DOWNLOAD_URL" ]; then
    echo "❌ 未提供下载链接，退出部署"
    exit 1
fi

# 下载文件
echo "⬇️ 下载网页文件..."
cd /var/www/html/
wget "$DOWNLOAD_URL" -O sim2real_website.zip

# 检查下载是否成功
if [ ! -f "sim2real_website.zip" ]; then
    echo "❌ 文件下载失败，请检查下载链接"
    exit 1
fi

# 解压文件
echo "📦 解压文件..."
unzip sim2real_website.zip

# 如果解压后文件在子目录中，移动到根目录
if [ -d "final_with_batch_analysis" ]; then
    mv final_with_batch_analysis/* .
    rmdir final_with_batch_analysis
fi

# 删除压缩包
rm sim2real_website.zip

# 设置文件权限
echo "🔐 设置文件权限..."
chown -R www-data:www-data /var/www/html/
chmod -R 755 /var/www/html/

# 配置Nginx
echo "⚙️ 配置Nginx..."
cat > /etc/nginx/sites-available/default << 'EOF'
server {
    listen 80 default_server;
    listen [::]:80 default_server;

    root /var/www/html;
    index sim2real_30k_improved.html index.html;

    server_name **************;

    location / {
        try_files $uri $uri/ =404;
    }

    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 设置图片缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF

# 创建默认页面链接
echo "🔗 设置默认页面..."
if [ -f "sim2real_30k_improved.html" ]; then
    ln -sf /var/www/html/sim2real_30k_improved.html /var/www/html/index.html
fi

# 测试Nginx配置
echo "🧪 测试Nginx配置..."
nginx -t

if [ $? -eq 0 ]; then
    # 重启Nginx
    echo "🔄 重启Nginx..."
    systemctl restart nginx
    
    echo ""
    echo "✅ 部署完成！"
    echo ""
    echo "🌐 访问地址："
    echo "   主页面: http://**************/sim2real_30k_improved.html"
    echo "   默认页: http://**************/"
    echo ""
    echo "📊 部署信息："
    echo "   服务器IP: **************"
    echo "   网页目录: /var/www/html/"
    echo "   Nginx状态: $(systemctl is-active nginx)"
    echo ""
    echo "🔧 管理命令："
    echo "   查看状态: sudo systemctl status nginx"
    echo "   重启服务: sudo systemctl restart nginx"
    echo "   查看日志: sudo tail -f /var/log/nginx/access.log"
    
else
    echo "❌ Nginx配置测试失败，请检查配置"
    exit 1
fi

# 显示部署的文件列表
echo ""
echo "📁 部署的文件："
ls -la /var/www/html/ | grep -E '\.(html|png)$'

echo ""
echo "🎉 Sim2Real_30k 数据集网页部署完成！"
