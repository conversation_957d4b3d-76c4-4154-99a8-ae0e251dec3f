# Sim2Real_30k 红外小目标检测数据集处理工具

## 📖 项目概述

本项目提供了一套完整的工具链，用于处理和分析 Sim2Real_30k 红外小目标检测数据集。该数据集包含30,000张高质量红外图像和76,582个精确标注的小目标，专门用于红外小目标检测算法的训练和评估。

## ✨ 主要功能

- 🔍 **数据集扫描**: 自动扫描和分析原始数据集结构
- 🔄 **智能数据处理**: 基于配置的数据筛选和格式转换
- 📊 **数据集划分**: 支持训练集/验证集划分
- 📈 **可视化分析**: 全面的数据集统计和可视化
- 🧠 **域差距分析**: 基于深度学习的batch间域差距分析
- 🔧 **跨平台支持**: Windows、Linux、macOS全平台兼容

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd Sim2Real

# 创建虚拟环境（推荐）
python -m venv sim2real_env
source sim2real_env/bin/activate  # Linux/Mac
# 或
sim2real_env\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置路径

```bash
# 运行路径配置助手
python scripts/setup_paths.py

# 或手动创建配置文件
cp config/paths_config_example.json config/paths_config.json
# 然后编辑 config/paths_config.json
```

### 3. 数据处理流程

```bash
# 1. 扫描数据集
python scripts/dataset_scanner.py -c config/paths_config.json -o scan_results.json

# 2. 处理数据
python scripts/data_processor.py scan_results.json -c config/processing_config.json

# 3. 划分数据集
python scripts/dataset_splitter.py "output/annotations/instances_complete.json" -o "output" --train-ratio 0.7

# 4. 生成可视化
python scripts/dataset_visualizer_en.py "output/annotations/instances_complete.json" -o "output/visualization"

# 5. 域差距分析
python scripts/domain_gap_analyzer.py "output/annotations/instances_complete.json" "output/images" -o "output/domain_analysis"
```

## 📁 项目结构

```
Sim2Real/
├── config/                     # 配置文件
│   ├── processing_config.json  # 数据处理配置
│   └── paths_config.json       # 路径配置
├── scripts/                    # 核心脚本
│   ├── setup_paths.py          # 路径配置助手
│   ├── config_utils.py         # 配置工具模块
│   ├── dataset_scanner.py      # 数据集扫描工具
│   ├── data_processor.py       # 数据处理管道
│   ├── dataset_splitter.py     # 数据集划分工具
│   ├── dataset_visualizer_en.py # 可视化工具（英文）
│   └── domain_gap_analyzer.py  # 域差距分析工具
├── docs/                       # 文档
│   ├── deployment_guide.md     # 部署指南
│   ├── visualization_usage_guide.md # 可视化使用指南
│   └── ...                     # 其他文档
├── requirements.txt            # Python依赖
└── README.md                   # 本文档
```

## 🔧 工具详细说明

### 数据集扫描器 (dataset_scanner.py)
- 扫描原始数据集结构
- 统计图像和标注文件
- 分析目标尺寸分布
- 检测数据质量问题

### 数据处理器 (data_processor.py)
- 智能图像筛选
- COCO格式转换
- 质量控制和验证
- 批量处理优化

### 数据集划分器 (dataset_splitter.py)
- 按batch划分避免数据泄露
- 支持自定义划分比例
- 生成训练集和验证集

### 可视化工具 (dataset_visualizer_en.py)
- 目标尺寸分布分析
- Batch间对比分析
- 空间分布热力图
- 样本可视化

### 域差距分析器 (domain_gap_analyzer.py)
- UNet特征提取
- Batch间相似性分析
- PCA降维可视化
- 聚类分析

## 📊 数据集统计

| 指标 | 数值 |
|------|------|
| 总图像数 | 30,000 |
| 总标注数 | 76,582 |
| 总batch数 | 50 |
| 平均每图目标数 | 2.55 |
| 目标尺寸范围 | 3-33像素 |
| 数据格式 | COCO JSON |

## 🎯 使用场景

### 学术研究
- 红外小目标检测算法开发
- 域适应和迁移学习研究
- 数据集基准测试

### 工业应用
- 海事监控系统
- 交通管理系统
- 安防监控系统

## 📋 配置说明

### 路径配置 (paths_config.json)
```json
{
  "dataset_root": "/path/to/your/dataset",
  "output_root": "/path/to/output",
  "temp_dir": "/path/to/temp"
}
```

### 处理配置 (processing_config.json)
```json
{
  "target_count": 30000,
  "frame_interval": [5, 10],
  "size_distribution": {
    "0-4px": 0.25,
    "5-9px": 0.35,
    "10-14px": 0.20
  }
}
```

## 🔄 跨平台部署

### Windows
```bash
python scripts\setup_paths.py
python scripts\dataset_scanner.py -p "K:\Sim2Real_30k"
```

### Linux/Unix
```bash
python scripts/setup_paths.py
python scripts/dataset_scanner.py -p "/mnt/data/Sim2Real_30k"
```

### macOS
```bash
python scripts/setup_paths.py
python scripts/dataset_scanner.py -p "/Volumes/Data/Sim2Real_30k"
```

## 🚨 常见问题

### 路径问题
- 使用引号包围包含空格的路径
- 确保对目标目录有读写权限
- 检查路径分隔符（Windows使用`\`，Unix使用`/`）

### 依赖问题
```bash
# 如果torch安装失败
pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu

# 如果出现权限问题
pip install --user package_name
```

### 内存问题
- 减少处理的图像数量
- 使用较小的batch size
- 关闭不必要的程序

## 📈 性能优化

- **GPU加速**: 域差距分析自动使用GPU（如果可用）
- **并行处理**: 数据处理支持多进程
- **内存优化**: 大数据集分批处理
- **缓存机制**: 避免重复计算

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 技术支持

如果遇到问题：
1. 查看 [docs/](docs/) 目录下的详细文档
2. 检查 [常见问题](#-常见问题) 部分
3. 提交 Issue 描述问题

## 🙏 致谢

感谢所有为数据集构建和工具开发做出贡献的研究人员。

---

**注意**: 首次使用时建议先在小数据集上测试，确认所有工具正常工作后再处理完整数据集。
