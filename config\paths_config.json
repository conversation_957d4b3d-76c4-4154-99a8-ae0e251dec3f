{"dataset_root": "K:\\Sim2Real_30k", "output_root": "K:\\Sim2Real_30k_output", "temp_dir": "K:\\temp\\sim2real", "platform": "windows", "created_by": "setup_paths.py", "version": "1.0", "subdirectories": {"images": "images", "annotations": "annotations", "reports": "reports", "visualization": "visualization", "domain_analysis": "domain_analysis"}, "file_patterns": {"scan_results": "scan_results.json", "complete_annotations": "instances_complete.json", "train_annotations": "instances_train_split.json", "val_annotations": "instances_val_split.json"}}