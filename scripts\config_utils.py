#!/usr/bin/env python3
"""
配置工具模块
提供统一的配置文件加载和路径管理功能
"""

import json
import os
from pathlib import Path
from typing import Dict, Optional, Union

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.paths_config = None
        self.processing_config = None
    
    def load_paths_config(self, config_file: Optional[str] = None) -> Optional[Dict]:
        """加载路径配置"""
        if config_file:
            config_path = Path(config_file)
        else:
            config_path = self.config_dir / "paths_config.json"
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                self.paths_config = json.load(f)
                return self.paths_config
        except FileNotFoundError:
            return None
        except Exception as e:
            print(f"⚠ 加载路径配置失败: {e}")
            return None
    
    def load_processing_config(self, config_file: Optional[str] = None) -> Optional[Dict]:
        """加载处理配置"""
        if config_file:
            config_path = Path(config_file)
        else:
            config_path = self.config_dir / "processing_config.json"
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                self.processing_config = json.load(f)
                return self.processing_config
        except FileNotFoundError:
            return None
        except Exception as e:
            print(f"⚠ 加载处理配置失败: {e}")
            return None
    
    def get_dataset_root(self) -> Optional[str]:
        """获取数据集根路径"""
        if not self.paths_config:
            self.load_paths_config()
        
        if self.paths_config:
            return self.paths_config.get('dataset_root')
        return None
    
    def get_output_root(self) -> Optional[str]:
        """获取输出根路径"""
        if not self.paths_config:
            self.load_paths_config()
        
        if self.paths_config:
            return self.paths_config.get('output_root')
        return None
    
    def get_temp_dir(self) -> Optional[str]:
        """获取临时目录路径"""
        if not self.paths_config:
            self.load_paths_config()
        
        if self.paths_config:
            return self.paths_config.get('temp_dir')
        return None
    
    def get_full_path(self, subdir: str, base: str = "output") -> Optional[str]:
        """获取完整路径"""
        if base == "output":
            root = self.get_output_root()
        elif base == "dataset":
            root = self.get_dataset_root()
        elif base == "temp":
            root = self.get_temp_dir()
        else:
            return None
        
        if root:
            return str(Path(root) / subdir)
        return None
    
    def ensure_directories(self) -> bool:
        """确保必要的目录存在"""
        if not self.paths_config:
            self.load_paths_config()
        
        if not self.paths_config:
            return False
        
        dirs_to_create = [
            self.get_output_root(),
            self.get_temp_dir(),
            self.get_full_path('images'),
            self.get_full_path('annotations'),
            self.get_full_path('reports'),
            self.get_full_path('visualization'),
            self.get_full_path('domain_analysis')
        ]
        
        success = True
        for dir_path in dirs_to_create:
            if dir_path:
                try:
                    Path(dir_path).mkdir(parents=True, exist_ok=True)
                except Exception as e:
                    print(f"⚠ 无法创建目录 {dir_path}: {e}")
                    success = False
        
        return success
    
    def validate_paths(self) -> Dict[str, bool]:
        """验证路径有效性"""
        if not self.paths_config:
            self.load_paths_config()
        
        validation = {}
        
        if self.paths_config:
            dataset_root = self.get_dataset_root()
            output_root = self.get_output_root()
            temp_dir = self.get_temp_dir()
            
            validation['dataset_root_exists'] = Path(dataset_root).exists() if dataset_root else False
            validation['output_root_writable'] = self._check_writable(output_root) if output_root else False
            validation['temp_dir_writable'] = self._check_writable(temp_dir) if temp_dir else False
        
        return validation
    
    def _check_writable(self, path: str) -> bool:
        """检查路径是否可写"""
        try:
            path_obj = Path(path)
            path_obj.mkdir(parents=True, exist_ok=True)
            test_file = path_obj / ".write_test"
            test_file.touch()
            test_file.unlink()
            return True
        except Exception:
            return False
    
    def get_standard_paths(self) -> Dict[str, str]:
        """获取标准路径字典"""
        if not self.paths_config:
            self.load_paths_config()
        
        if not self.paths_config:
            return {}
        
        return {
            'dataset_root': self.get_dataset_root() or "",
            'output_root': self.get_output_root() or "",
            'temp_dir': self.get_temp_dir() or "",
            'images_dir': self.get_full_path('images') or "",
            'annotations_dir': self.get_full_path('annotations') or "",
            'reports_dir': self.get_full_path('reports') or "",
            'visualization_dir': self.get_full_path('visualization') or "",
            'domain_analysis_dir': self.get_full_path('domain_analysis') or ""
        }

def load_config(config_type: str = "paths", config_file: Optional[str] = None) -> Optional[Dict]:
    """便捷函数：加载配置"""
    manager = ConfigManager()
    
    if config_type == "paths":
        return manager.load_paths_config(config_file)
    elif config_type == "processing":
        return manager.load_processing_config(config_file)
    else:
        return None

def get_paths_from_config(config_file: Optional[str] = None) -> Dict[str, str]:
    """便捷函数：从配置获取路径"""
    manager = ConfigManager()
    manager.load_paths_config(config_file)
    return manager.get_standard_paths()

def resolve_path(path_input: Union[str, None], config_key: str, config_file: Optional[str] = None) -> Optional[str]:
    """解析路径：优先使用输入路径，否则从配置加载"""
    if path_input:
        return path_input
    
    config = load_config("paths", config_file)
    if config:
        return config.get(config_key)
    
    return None

def create_example_config():
    """创建示例配置文件"""
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    # 路径配置示例
    paths_config = {
        "dataset_root": "/path/to/your/dataset",
        "output_root": "/path/to/output",
        "temp_dir": "/path/to/temp",
        "platform": "auto-detect",
        "created_by": "config_utils.py",
        "subdirectories": {
            "images": "images",
            "annotations": "annotations",
            "reports": "reports",
            "visualization": "visualization",
            "domain_analysis": "domain_analysis"
        },
        "file_patterns": {
            "scan_results": "scan_results.json",
            "complete_annotations": "instances_complete.json",
            "train_annotations": "instances_train_split.json",
            "val_annotations": "instances_val_split.json"
        }
    }
    
    # 保存示例配置
    with open(config_dir / "paths_config_example.json", 'w', encoding='utf-8') as f:
        json.dump(paths_config, f, ensure_ascii=False, indent=2)
    
    print(f"✓ 示例配置已创建: {config_dir / 'paths_config_example.json'}")
    print("请复制并修改为 paths_config.json")

if __name__ == "__main__":
    # 测试配置管理器
    manager = ConfigManager()
    
    # 尝试加载配置
    paths = manager.load_paths_config()
    if paths:
        print("✓ 路径配置加载成功")
        print(f"数据集路径: {manager.get_dataset_root()}")
        print(f"输出路径: {manager.get_output_root()}")
        
        # 验证路径
        validation = manager.validate_paths()
        print(f"路径验证结果: {validation}")
    else:
        print("⚠ 未找到路径配置，创建示例配置...")
        create_example_config()
