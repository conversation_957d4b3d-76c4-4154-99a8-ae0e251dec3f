# Dataset Visualization and Domain Gap Analysis Usage Guide

## Overview

This guide covers the usage of two main visualization tools for the Sim2Real_30k infrared small target detection dataset:

1. **Dataset Visualizer** - Comprehensive dataset statistics and distribution analysis
2. **Domain Gap Analyzer** - Deep learning-based domain gap analysis between batches

## 🎯 Dataset Visualizer

### Features
- ✅ Target size distribution analysis (histogram, pie chart, CDF, box plot)
- ✅ Inter-batch comparison (image counts, annotation counts, target density)
- ✅ Spatial distribution heatmaps
- ✅ Sample visualization with simulated infrared images
- ✅ Comprehensive statistical reports

### Usage

#### Basic Usage
```bash
# English version (recommended - no font issues)
python scripts/dataset_visualizer_en.py "K:\Sim2Real_30k\annotations\instances_complete.json" -o "K:\Sim2Real_30k\visualization_en"

# Full version with all features
python scripts/dataset_visualizer.py "K:\Sim2Real_30k\annotations\instances_complete.json" -o "K:\Sim2Real_30k\visualization" --samples 16
```

#### Output Files
```
visualization_output/
├── size_distribution_en.png      # Target size analysis
├── batch_comparison_en.png       # Inter-batch comparison
├── spatial_distribution.png      # Spatial distribution heatmap
├── sample_visualization.png      # Dataset samples with annotations
├── domain_analysis.png           # Traditional domain gap analysis
└── dataset_summary_report.json   # Comprehensive statistics report
```

## 🧠 Domain Gap Analyzer (Deep Learning-based)

### Features
- ✅ **UNet-based feature extraction** - Uses a simplified UNet to extract deep features
- ✅ **Batch-level domain analysis** - Analyzes feature differences between batches
- ✅ **Multiple visualization methods**:
  - Feature similarity heatmap
  - PCA dimensionality reduction
  - Pairwise distance distribution
  - K-means clustering analysis
- ✅ **Comprehensive reporting** - Detailed domain gap statistics and recommendations

### Usage

#### Basic Usage
```bash
# Analyze domain gap using UNet features
python scripts/domain_gap_analyzer.py "K:\Sim2Real_30k\annotations\instances_complete.json" "K:\Sim2Real_30k\images" -o "K:\Sim2Real_30k\domain_analysis" --max-images 10

# For faster analysis (fewer images per batch)
python scripts/domain_gap_analyzer.py "K:\Sim2Real_30k\annotations\instances_complete.json" "K:\Sim2Real_30k\images" -o "K:\Sim2Real_30k\domain_analysis" --max-images 5
```

#### Parameters
- `annotation_file`: Path to COCO annotation file
- `image_dir`: Directory containing the actual image files
- `-o, --output`: Output directory for results
- `--max-images`: Maximum number of images to analyze per batch (default: 20)

#### Output Files
```
domain_analysis_output/
├── domain_gap_analysis.png        # Comprehensive domain gap visualization
└── domain_gap_report.json         # Detailed analysis report
```

### Technical Details

#### Feature Extraction Method
1. **UNet Architecture**: Simplified UNet with encoder-decoder structure
2. **Input Processing**: Images are converted to grayscale and resized to 256x256
3. **Feature Extraction**: Global average pooling on the deepest encoder features
4. **Batch Representation**: Average features across selected images per batch

#### Analysis Methods
1. **Similarity Matrix**: Correlation between batch feature vectors
2. **PCA Visualization**: 2D projection of high-dimensional features
3. **Distance Analysis**: Euclidean distances between batch representations
4. **Clustering**: K-means clustering to identify similar batches

## 📊 Understanding the Results

### Size Distribution Analysis
- **Histogram**: Shows the frequency distribution of target sizes
- **Pie Chart**: Categorizes targets into size ranges (tiny, small, medium, large)
- **CDF**: Cumulative distribution function for percentile analysis
- **Box Plot**: Statistical summary with quartiles and outliers

### Domain Gap Analysis
- **High Similarity (>0.8)**: Batches with very similar visual characteristics
- **Medium Similarity (0.5-0.8)**: Moderate domain differences
- **Low Similarity (<0.5)**: Significant domain gaps requiring attention

### Recommendations Based on Results

#### High Domain Diversity (Recommended)
- **Characteristics**: Large standard deviation in pairwise distances
- **Benefits**: Better generalization capability
- **Training Strategy**: Standard training approaches work well

#### Low Domain Diversity (Needs Attention)
- **Characteristics**: Small standard deviation, high similarity
- **Risks**: Potential overfitting to specific domains
- **Training Strategy**: 
  - Implement domain adaptation techniques
  - Use batch-aware sampling
  - Consider progressive training across batches

## 🔧 Installation Requirements

### Basic Requirements
```bash
pip install numpy matplotlib seaborn Pillow
```

### For Domain Gap Analysis
```bash
pip install torch torchvision scikit-learn
```

### Optional (for enhanced features)
```bash
pip install tqdm  # Progress bars
```

## 🚀 Advanced Usage

### Custom Configuration
You can modify the analysis parameters by editing the configuration in the scripts:

```python
# In domain_gap_analyzer.py
class SimpleUNet(nn.Module):
    def __init__(self, in_channels=1, out_channels=64):  # Adjust feature dimensions
        # ... modify architecture as needed

# In dataset_visualizer.py
size_ranges = {
    'Tiny Targets (3-8px)': (3, 8),      # Customize size categories
    'Small Targets (8-16px)': (8, 16),
    # ... add more ranges as needed
}
```

### Batch Processing
For large datasets, you can process batches separately:

```bash
# Process specific batch ranges
python scripts/domain_gap_analyzer.py annotations.json images/ -o output/ --max-images 5

# Combine results from multiple runs
# (Manual combination of JSON reports)
```

## 📈 Integration with Research

### For Academic Papers
1. **Use the generated visualizations** in your dataset introduction section
2. **Include domain gap analysis** to demonstrate dataset diversity
3. **Reference the statistical reports** for quantitative analysis
4. **Compare with existing datasets** using similar metrics

### For Model Development
1. **Use domain gap insights** to design training strategies
2. **Implement batch-aware sampling** based on similarity analysis
3. **Design domain adaptation methods** for high-gap scenarios
4. **Validate model robustness** across different domain clusters

## 🔍 Troubleshooting

### Common Issues

#### Memory Issues
- Reduce `--max-images` parameter
- Use CPU instead of GPU for feature extraction
- Process batches separately

#### Font Warnings
- Use the English version (`dataset_visualizer_en.py`)
- Install additional fonts if needed
- Warnings don't affect functionality

#### Missing Dependencies
```bash
# Install missing packages
pip install torch torchvision  # For domain gap analysis
pip install scikit-learn       # For PCA and clustering
```

### Performance Optimization
- **GPU Usage**: Automatically detected for faster feature extraction
- **Batch Size**: Adjust based on available memory
- **Image Resolution**: 256x256 provides good balance of speed and quality

## 📝 Citation

If you use these visualization tools in your research, please cite:

```bibtex
@dataset{sim2real_30k,
  title={Sim2Real_30k: A Large-Scale Infrared Small Target Detection Dataset},
  author={Your Name},
  year={2024},
  note={Dataset visualization and domain gap analysis tools}
}
```

---

For more information or issues, please refer to the project documentation or contact the development team.
