#!/usr/bin/env python3
"""
Show cross-dataset analysis summary
"""
import json
from pathlib import Path

def show_summary():
    summary_file = "K:/Sim2Real_30k/cross_dataset_report/cross_dataset_summary_statistics.json"
    
    if not Path(summary_file).exists():
        print("❌ Summary file not found")
        return
    
    with open(summary_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print("🔬 跨数据集域差异分析摘要")
    print("=" * 50)
    
    # Analysis summary
    analysis = data['analysis_summary']
    print(f"📅 分析时间: {analysis['timestamp']}")
    print(f"📊 数据集数量: {analysis['datasets_analyzed']}")
    print(f"🔗 相似性对数: {analysis['total_similarity_pairs']}")
    
    # Dataset overview
    print(f"\n📁 数据集概览:")
    for name, info in data['dataset_overview'].items():
        print(f"  • {name}: {info['type']} (特征维度: {info['feature_dimension']})")
    
    # Similarity insights
    if 'similarity_insights' in data:
        insights = data['similarity_insights']
        print(f"\n🎯 相似性洞察:")
        print(f"  • 平均相似性: {insights['average_similarity']:.3f}")
        print(f"  • 最高相似性: {insights['max_similarity']:.3f}")
        print(f"  • 最低相似性: {insights['min_similarity']:.3f}")
        print(f"  • 标准差: {insights['std_similarity']:.3f}")
        
        most_similar = insights['most_similar_pair']
        least_similar = insights['least_similar_pair']
        print(f"\n🔝 最相似对: {most_similar['pair'].replace('_vs_', ' ↔ ')} ({most_similar['score']:.3f})")
        print(f"🔻 最不相似对: {least_similar['pair'].replace('_vs_', ' ↔ ')} ({least_similar['score']:.3f})")
    
    # Key findings
    if 'key_findings' in data and data['key_findings']:
        print(f"\n💡 关键发现:")
        for finding in data['key_findings']:
            print(f"  • {finding}")
    
    print(f"\n✅ 分析完成！查看完整报告:")
    print(f"🌐 file:///K:/Sim2Real_30k/cross_dataset_report/cross_dataset_comprehensive_report.html")

if __name__ == "__main__":
    show_summary()
