#!/usr/bin/env python3
"""
数据集扫描脚本
扫描指定路径下的所有图像-标签对，统计数据分布情况
"""

import os
import json
import re
import sys
import argparse
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Tuple, Optional

try:
    from tqdm import tqdm
except ImportError:
    print("警告: 未安装tqdm库，将使用简单进度显示")
    print("建议安装: pip install tqdm")
    tqdm = None

class DatasetScanner:
    def __init__(self, root_path: str, verbose: bool = True):
        self.root_path = Path(root_path)
        self.verbose = verbose
        self.stats = {
            'total_folders': 0,
            'valid_batches': 0,
            'total_pairs': 0,
            'valid_targets': 0,
            'size_distribution': defaultdict(int),
            'batch_info': [],
            'error_summary': defaultdict(int)  # 添加错误统计
        }

        # 验证路径是否存在
        if not self.root_path.exists():
            raise FileNotFoundError(f"指定路径不存在: {self.root_path}")
        if not self.root_path.is_dir():
            raise NotADirectoryError(f"指定路径不是文件夹: {self.root_path}")

        if self.verbose:
            print(f"✓ 扫描路径验证成功: {self.root_path}")
            print(f"✓ 开始收集文件夹信息...")
    
    def find_image_label_pairs(self, folder_path: Path) -> List[Tuple[Path, Path]]:
        """在文件夹中查找图像-标签配对"""
        pairs = []
        
        # 获取所有png和json文件
        png_files = list(folder_path.glob("*.png"))
        json_files = list(folder_path.glob("*.json"))
        
        # 模式1: 相同基础名称
        for png_file in png_files:
            base_name = png_file.stem
            json_file = folder_path / f"{base_name}.json"
            if json_file.exists():
                pairs.append((png_file, json_file))
        
        # 模式2: output_images + video_label
        output_pattern = re.compile(r'output_images(\d+)\.png')
        for png_file in png_files:
            match = output_pattern.match(png_file.name)
            if match:
                num = match.group(1)
                json_file = folder_path / f"video_label{num}.json"
                if json_file.exists():
                    pairs.append((png_file, json_file))
        
        return pairs
    
    def analyze_json_targets(self, json_path: Path) -> Dict:
        """分析JSON文件中的目标"""
        try:
            # 检查文件大小
            if json_path.stat().st_size == 0:
                return {'valid_targets': 0, 'sizes': [], 'error': 'empty_file'}

            with open(json_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    return {'valid_targets': 0, 'sizes': [], 'error': 'empty_content'}

                data = json.loads(content)

            # 处理两种数据格式
            targets = []
            if isinstance(data, list):
                # 格式1: 直接是目标数组 (旧格式)
                targets = data
            elif isinstance(data, dict) and 'units' in data:
                # 格式2: 包含units字段的字典 (新格式)
                targets = data['units']
            else:
                return {'valid_targets': 0, 'sizes': [], 'error': 'unknown_format'}

            valid_targets = 0
            sizes = []

            for target in targets:
                # 检查是否包含bbox字段
                if 'bbox' not in target:
                    continue

                # 对于旧格式，检查飞机类型和状态
                if 'm_blk_path' in target:
                    # 旧格式筛选
                    blk_path = target.get('m_blk_path', '')
                    if blk_path is None or 'gameData/flightModels/' not in blk_path:
                        continue
                    if target.get('isdead', True) or not target.get('isvisible', False):
                        continue

                # 计算目标尺寸
                bbox = target['bbox']
                if len(bbox) >= 4:
                    width = abs(bbox[2] - bbox[0])
                    height = abs(bbox[3] - bbox[1])
                    size = max(width, height)

                    if 3 <= size <= 33:  # 尺寸筛选
                        valid_targets += 1
                        sizes.append(size)

            return {'valid_targets': valid_targets, 'sizes': sizes}

        except json.JSONDecodeError as e:
            return {'valid_targets': 0, 'sizes': [], 'error': f'json_decode_error: {str(e)[:50]}'}
        except Exception as e:
            return {'valid_targets': 0, 'sizes': [], 'error': f'other_error: {str(e)[:50]}'}
    
    def scan_folder(self, folder_path: Path) -> Optional[Dict]:
        """扫描单个文件夹"""
        pairs = self.find_image_label_pairs(folder_path)
        if not pairs:
            return None

        batch_info = {
            'path': str(folder_path),
            'total_pairs': len(pairs),
            'valid_pairs': 0,
            'total_targets': 0,
            'size_distribution': defaultdict(int),
            'error_count': 0
        }

        for img_path, json_path in pairs:
            analysis = self.analyze_json_targets(json_path)

            # 统计错误
            if 'error' in analysis:
                batch_info['error_count'] += 1
                self.stats['error_summary'][analysis['error']] += 1
                # 只在verbose模式下显示前几个错误
                if self.verbose and self.stats['error_summary'][analysis['error']] <= 3:
                    print(f"⚠ JSON错误 ({analysis['error']}): {json_path.name}")

            if analysis['valid_targets'] > 0:
                batch_info['valid_pairs'] += 1
                batch_info['total_targets'] += analysis['valid_targets']

                for size in analysis['sizes']:
                    size_range = f"{int(size//5)*5}-{int(size//5)*5+4}px"
                    batch_info['size_distribution'][size_range] += 1

        return batch_info if batch_info['valid_pairs'] > 0 else None
    
    def collect_all_folders(self) -> List[Path]:
        """收集所有需要扫描的文件夹"""
        all_folders = []
        folder_count = 0

        if self.verbose:
            print("正在收集文件夹列表...")

        for folder_path in self.root_path.rglob("*"):
            if folder_path.is_dir():
                all_folders.append(folder_path)
                folder_count += 1

                # 每50个文件夹显示一次进度
                if self.verbose and folder_count % 50 == 0:
                    print(f"已发现 {folder_count} 个文件夹...")

        return all_folders

    def scan_all(self) -> Dict:
        """扫描所有文件夹"""
        if self.verbose:
            print(f"开始扫描: {self.root_path}")

        # 首先收集所有文件夹，包括根目录本身
        all_folders = self.collect_all_folders()

        # 如果根目录直接包含图像文件，也要扫描根目录
        root_pairs = self.find_image_label_pairs(self.root_path)
        if root_pairs:
            all_folders.insert(0, self.root_path)  # 将根目录放在最前面

        if self.verbose:
            print(f"✓ 发现 {len(all_folders)} 个文件夹需要扫描")

        # 使用进度条扫描
        if tqdm is not None:
            folder_iterator = tqdm(all_folders, desc="扫描文件夹", unit="folder")
        else:
            folder_iterator = all_folders
            if self.verbose:
                print("开始逐个扫描文件夹...")

        for i, folder_path in enumerate(folder_iterator):
            self.stats['total_folders'] += 1

            # 简单进度显示（当没有tqdm时）
            if tqdm is None and self.verbose and (i + 1) % 10 == 0:
                print(f"已扫描 {i + 1}/{len(all_folders)} 个文件夹...")

            batch_info = self.scan_folder(folder_path)
            if batch_info:
                self.stats['valid_batches'] += 1
                self.stats['total_pairs'] += batch_info['total_pairs']
                self.stats['valid_targets'] += batch_info['total_targets']
                self.stats['batch_info'].append(batch_info)

                # 更新全局尺寸分布
                for size_range, count in batch_info['size_distribution'].items():
                    self.stats['size_distribution'][size_range] += count

                if self.verbose and tqdm is None:
                    print(f"✓ Batch {self.stats['valid_batches']}: {folder_path.name} "
                          f"({batch_info['valid_pairs']} pairs, {batch_info['total_targets']} targets)")

        if self.verbose:
            print(f"\n✓ 扫描完成！")

        return self.stats
    
    def print_summary(self):
        """打印扫描结果摘要"""
        print("\n" + "="*60)
        print("扫描结果摘要")
        print("="*60)
        print(f"总文件夹数: {self.stats['total_folders']}")
        print(f"有效batch数: {self.stats['valid_batches']}")
        print(f"总图像-标签对: {self.stats['total_pairs']}")
        print(f"有效目标总数: {self.stats['valid_targets']}")

        # 显示错误统计
        total_errors = 0
        if self.stats['error_summary']:
            print(f"\nJSON文件错误统计:")
            total_errors = sum(self.stats['error_summary'].values())
            print(f"  总错误数: {total_errors}")
            for error_type, count in sorted(self.stats['error_summary'].items()):
                print(f"  {error_type}: {count}")

        if self.stats['size_distribution']:
            print(f"\n目标尺寸分布:")
            for size_range in sorted(self.stats['size_distribution'].keys()):
                count = self.stats['size_distribution'][size_range]
                print(f"  {size_range}: {count}")
        else:
            print(f"\n⚠ 未发现有效目标")

        print(f"\n预估存储需求:")
        estimated_images = sum(batch['valid_pairs'] for batch in self.stats['batch_info'])
        estimated_size_gb = estimated_images * 8 / 1024  # 8MB per image
        print(f"  预计筛选图像数: {estimated_images}")
        print(f"  预计存储空间: {estimated_size_gb:.1f} GB")

        # 给出建议
        if total_errors > 0 and estimated_images == 0:
            print(f"\n💡 建议:")
            print(f"  - 检查JSON文件格式是否正确")
            print(f"  - 确认数据集结构是否符合预期")
            print(f"  - 可能需要检查具体的错误文件")

def get_user_input_path() -> str:
    """获取用户输入的扫描路径"""
    print("=" * 60)
    print("红外小目标检测数据集扫描工具")
    print("=" * 60)

    while True:
        print("\n请输入要扫描的数据集路径:")
        print("示例: G:\\孤立的文件\\孤立的文件 45")
        print("或者: /mnt/dataset/batch1")

        user_path = input("路径: ").strip()

        if not user_path:
            print("❌ 路径不能为空，请重新输入")
            continue

        # 移除引号（如果用户复制粘贴时包含了引号）
        user_path = user_path.strip('"\'')

        path_obj = Path(user_path)
        if not path_obj.exists():
            print(f"❌ 路径不存在: {user_path}")
            retry = input("是否重新输入？(y/n): ").strip().lower()
            if retry != 'y':
                print("退出程序")
                sys.exit(1)
            continue

        if not path_obj.is_dir():
            print(f"❌ 指定路径不是文件夹: {user_path}")
            retry = input("是否重新输入？(y/n): ").strip().lower()
            if retry != 'y':
                print("退出程序")
                sys.exit(1)
            continue

        print(f"✓ 路径验证成功: {user_path}")
        return user_path

def load_paths_config(config_file="config/paths_config.json"):
    """加载路径配置文件"""
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return None
    except Exception as e:
        print(f"⚠ 加载配置文件失败: {e}")
        return None

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="红外小目标检测数据集扫描工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python dataset_scanner.py                           # 交互式输入路径
  python dataset_scanner.py -p "G:\\data\\batch1"     # 直接指定路径
  python dataset_scanner.py --path /mnt/dataset       # Linux路径示例
  python dataset_scanner.py -p "D:\\data" -o results  # 指定输出文件名
  python dataset_scanner.py -c config/paths_config.json  # 使用配置文件
        """
    )

    parser.add_argument(
        '-p', '--path',
        type=str,
        help='数据集根路径（如果不指定则交互式输入）'
    )

    parser.add_argument(
        '-o', '--output',
        type=str,
        default='scan_results.json',
        help='输出文件名（默认: scan_results.json）'
    )

    parser.add_argument(
        '-c', '--config',
        type=str,
        help='路径配置文件'
    )

    parser.add_argument(
        '-q', '--quiet',
        action='store_true',
        help='静默模式，减少输出信息'
    )

    return parser.parse_args()

def main():
    args = parse_arguments()

    try:
        # 获取扫描路径
        root_path = None

        # 1. 优先使用命令行参数
        if args.path:
            root_path = args.path
            print(f"使用指定路径: {root_path}")

        # 2. 尝试从配置文件加载路径
        elif args.config:
            config = load_paths_config(args.config)
            if config and 'dataset_root' in config:
                root_path = config['dataset_root']
                print(f"从配置文件加载路径: {root_path}")
            else:
                print(f"⚠ 配置文件中未找到dataset_root")

        # 3. 尝试从默认配置文件加载路径
        elif not root_path:
            config = load_paths_config()
            if config and 'dataset_root' in config:
                root_path = config['dataset_root']
                print(f"从默认配置文件加载路径: {root_path}")

        # 4. 交互式输入
        if not root_path:
            root_path = get_user_input_path()

        # 创建扫描器并执行扫描
        verbose = not args.quiet
        scanner = DatasetScanner(root_path, verbose=verbose)

        print(f"\n开始扫描数据集...")
        stats = scanner.scan_all()
        scanner.print_summary()

        # 保存详细结果
        output_file = args.output
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2, default=str)

        print(f"\n✓ 详细结果已保存到: {output_file}")

    except KeyboardInterrupt:
        print("\n\n用户中断扫描")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 扫描过程中出现错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()