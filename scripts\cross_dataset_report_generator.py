#!/usr/bin/env python3
"""
Cross-Dataset Domain Gap Analysis Report Generator
Generate comprehensive HTML report for cross-dataset analysis
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import argparse
import base64
from datetime import datetime
from typing import Dict, List, Optional

# Set style
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

class CrossDatasetReportGenerator:
    def __init__(self, analysis_dir: str, output_dir: str):
        self.analysis_dir = Path(analysis_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Load analysis results
        self.report_data = self.load_analysis_report()
        self.visualization_files = self.find_visualization_files()
        
    def load_analysis_report(self) -> Optional[Dict]:
        """Load cross-dataset analysis report"""
        report_file = self.analysis_dir / "cross_dataset_analysis_report.json"
        if report_file.exists():
            with open(report_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None
    
    def find_visualization_files(self) -> Dict[str, Path]:
        """Find visualization files"""
        files = {}
        for file in self.analysis_dir.glob("*.png"):
            files[file.stem] = file
        return files
    
    def encode_image_to_base64(self, image_path: Path) -> str:
        """Convert image to base64 for HTML embedding"""
        with open(image_path, 'rb') as f:
            image_data = f.read()
        return base64.b64encode(image_data).decode('utf-8')
    
    def create_dataset_info_section(self) -> str:
        """Create dataset information section"""
        if not self.report_data or 'dataset_info' not in self.report_data:
            return ""
        
        dataset_info = self.report_data['dataset_info']
        
        html = """
        <div class="section">
            <h2 class="section-title">📊 Dataset Information</h2>
            <div class="dataset-grid">
"""
        
        # Dataset descriptions from web search
        dataset_descriptions = {
            'Sim2Real_30k': 'Sim2Real 30k infrared small target dataset with 30,000 images and 76,582 annotations',
            'DenseSIRST': 'Dense clustered infrared small target dataset for background semantics analysis',
            'IRSatVideo-LEO': 'Infrared satellite video dataset for moving infrared small target detection',
            'IRSTD-1k': 'IRSTD-1k realistic infrared small target dataset with 1,001 manually labeled images',
            'NoisySIRST': 'Noisy SIRST dataset with random noise for robustness evaluation',
            'NUAA-SIRST': 'NUAA single-frame infrared small target dataset',
            'NUDT-SIRST': 'NUDT infrared small target dataset with dense nested attention',
            'WideIRSTD': 'Wide-area infrared small target dataset for large-scale detection'
        }
        
        for dataset_name, info in dataset_info.items():
            description = dataset_descriptions.get(dataset_name, info.get('description', 'No description available'))
            
            html += f"""
                <div class="dataset-card">
                    <h3>{dataset_name}</h3>
                    <p class="dataset-description">{description}</p>
                    <div class="dataset-stats">
                        <div class="stat-item">
                            <span class="stat-label">Type:</span>
                            <span class="stat-value">{info.get('type', 'Unknown')}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Feature Dim:</span>
                            <span class="stat-value">{info.get('feature_dimension', 'N/A')}</span>
                        </div>
                    </div>
                </div>
"""
        
        html += """
            </div>
        </div>
"""
        return html
    
    def create_similarity_analysis_section(self) -> str:
        """Create similarity analysis section"""
        if not self.report_data or 'similarity_analysis' not in self.report_data:
            return ""
        
        similarity_data = self.report_data['similarity_analysis']
        
        html = """
        <div class="section">
            <h2 class="section-title">🔍 Similarity Analysis</h2>
            <div class="similarity-content">
"""
        
        # Add similarity matrix visualization
        if 'cross_dataset_similarity_matrix' in self.visualization_files:
            img_path = self.visualization_files['cross_dataset_similarity_matrix']
            img_base64 = self.encode_image_to_base64(img_path)
            html += f"""
                <div class="viz-container">
                    <h3>Dataset Similarity Matrix</h3>
                    <img src="data:image/png;base64,{img_base64}" alt="Similarity Matrix" class="viz-image">
                </div>
"""
        
        # Top similar pairs
        similarity_pairs = [(k, v) for k, v in similarity_data.items()]
        similarity_pairs.sort(key=lambda x: x[1]['similarity_score'], reverse=True)
        
        html += """
                <div class="similarity-table-container">
                    <h3>Top Similar Dataset Pairs</h3>
                    <table class="similarity-table">
                        <thead>
                            <tr>
                                <th>Dataset Pair</th>
                                <th>Similarity Score</th>
                                <th>Relationship</th>
                            </tr>
                        </thead>
                        <tbody>
"""
        
        for pair_name, pair_data in similarity_pairs[:10]:  # Top 10
            score = pair_data['similarity_score']
            relationship = pair_data['relationship']
            
            # Color code based on similarity
            if score > 0.8:
                row_class = "high-similarity"
            elif score > 0.6:
                row_class = "medium-similarity"
            else:
                row_class = "low-similarity"
            
            html += f"""
                            <tr class="{row_class}">
                                <td>{pair_name.replace('_vs_', ' ↔ ')}</td>
                                <td>{score:.3f}</td>
                                <td>{relationship}</td>
                            </tr>
"""
        
        html += """
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
"""
        return html
    
    def create_pca_analysis_section(self) -> str:
        """Create PCA analysis section"""
        html = """
        <div class="section">
            <h2 class="section-title">📈 Principal Component Analysis</h2>
            <div class="pca-content">
"""
        
        if 'cross_dataset_pca_visualization' in self.visualization_files:
            img_path = self.visualization_files['cross_dataset_pca_visualization']
            img_base64 = self.encode_image_to_base64(img_path)
            html += f"""
                <div class="viz-container">
                    <img src="data:image/png;base64,{img_base64}" alt="PCA Visualization" class="viz-image">
                    <p class="viz-description">
                        PCA visualization shows the relationships between datasets in a 2D space. 
                        Datasets that are closer together have more similar feature representations.
                    </p>
                </div>
"""
        
        html += """
            </div>
        </div>
"""
        return html
    
    def create_recommendations_section(self) -> str:
        """Create recommendations section"""
        if not self.report_data or 'recommendations' not in self.report_data:
            return ""
        
        recommendations = self.report_data['recommendations']
        
        html = """
        <div class="section">
            <h2 class="section-title">💡 Recommendations</h2>
            <div class="recommendations-content">
                <ul class="recommendations-list">
"""
        
        for rec in recommendations:
            html += f"<li>{rec}</li>"
        
        html += """
                </ul>
            </div>
        </div>
"""
        return html
    
    def create_html_report(self) -> str:
        """Create comprehensive HTML report"""
        if not self.report_data:
            return "<html><body><h1>No analysis data found</h1></body></html>"
        
        analysis_time = self.report_data.get('analysis_timestamp', 'Unknown')
        datasets_count = self.report_data.get('datasets_analyzed', 0)
        
        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cross-Dataset Domain Gap Analysis Report</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
            line-height: 1.6;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 50px;
            padding-bottom: 30px;
            border-bottom: 3px solid #667eea;
        }}
        .header h1 {{
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 2.5em;
        }}
        .header .subtitle {{
            color: #7f8c8d;
            font-size: 1.2em;
        }}
        .stats-overview {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }}
        .stat-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
        }}
        .stat-value {{
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 8px;
        }}
        .stat-label {{
            font-size: 1.1em;
            opacity: 0.9;
        }}
        .section {{
            margin-bottom: 50px;
        }}
        .section-title {{
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 30px;
            font-size: 1.8em;
        }}
        .dataset-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
        }}
        .dataset-card {{
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            transition: transform 0.3s ease;
        }}
        .dataset-card:hover {{
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }}
        .dataset-card h3 {{
            color: #495057;
            margin-bottom: 10px;
        }}
        .dataset-description {{
            color: #6c757d;
            margin-bottom: 15px;
            font-size: 0.95em;
        }}
        .dataset-stats {{
            display: flex;
            justify-content: space-between;
        }}
        .stat-item {{
            display: flex;
            flex-direction: column;
        }}
        .stat-label {{
            font-size: 0.85em;
            color: #6c757d;
        }}
        .stat-value {{
            font-weight: bold;
            color: #495057;
        }}
        .viz-container {{
            text-align: center;
            margin-bottom: 30px;
        }}
        .viz-image {{
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }}
        .viz-description {{
            color: #6c757d;
            font-style: italic;
            margin-top: 15px;
        }}
        .similarity-table-container {{
            margin-top: 30px;
        }}
        .similarity-table {{
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }}
        .similarity-table th,
        .similarity-table td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }}
        .similarity-table th {{
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }}
        .high-similarity {{
            background-color: #d4edda;
        }}
        .medium-similarity {{
            background-color: #fff3cd;
        }}
        .low-similarity {{
            background-color: #f8d7da;
        }}
        .recommendations-list {{
            list-style-type: none;
            padding: 0;
        }}
        .recommendations-list li {{
            background: #e3f2fd;
            margin-bottom: 10px;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }}
        .footer {{
            text-align: center;
            margin-top: 50px;
            padding-top: 30px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔬 Cross-Dataset Domain Gap Analysis</h1>
            <p class="subtitle">Comprehensive Analysis of Infrared Small Target Detection Datasets</p>
            <p>Analysis completed: {analysis_time}</p>
        </div>
        
        <div class="stats-overview">
            <div class="stat-card">
                <div class="stat-value">{datasets_count}</div>
                <div class="stat-label">Datasets Analyzed</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{len(self.report_data.get('similarity_analysis', {}))}</div>
                <div class="stat-label">Similarity Pairs</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{len(self.visualization_files)}</div>
                <div class="stat-label">Visualizations</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{len(self.report_data.get('recommendations', []))}</div>
                <div class="stat-label">Recommendations</div>
            </div>
        </div>
"""
        
        # Add sections
        html_content += self.create_dataset_info_section()
        html_content += self.create_similarity_analysis_section()
        html_content += self.create_pca_analysis_section()
        html_content += self.create_recommendations_section()
        
        # Close HTML
        html_content += """
        <div class="footer">
            <p>Generated by Cross-Dataset Domain Gap Analysis Tool</p>
            <p>For research and development in infrared small target detection</p>
        </div>
    </div>
</body>
</html>"""
        
        return html_content

    def generate_comprehensive_report(self):
        """Generate comprehensive cross-dataset analysis report"""
        print("🚀 Generating comprehensive cross-dataset analysis report...")

        if not self.report_data:
            print("❌ No analysis data found. Please run cross-dataset analysis first.")
            return None

        # Create HTML report
        html_content = self.create_html_report()

        # Save report
        report_file = self.output_dir / "cross_dataset_comprehensive_report.html"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"✅ Comprehensive report generated: {report_file}")

        # Generate summary statistics
        self.generate_summary_statistics()

        return report_file

    def generate_summary_statistics(self):
        """Generate summary statistics file"""
        if not self.report_data:
            return

        summary = {
            'analysis_summary': {
                'timestamp': self.report_data.get('analysis_timestamp'),
                'datasets_analyzed': self.report_data.get('datasets_analyzed', 0),
                'total_similarity_pairs': len(self.report_data.get('similarity_analysis', {}))
            },
            'dataset_overview': {},
            'similarity_insights': {},
            'key_findings': []
        }

        # Dataset overview
        for name, info in self.report_data.get('dataset_info', {}).items():
            summary['dataset_overview'][name] = {
                'type': info.get('type'),
                'feature_dimension': info.get('feature_dimension')
            }

        # Similarity insights
        similarity_data = self.report_data.get('similarity_analysis', {})
        if similarity_data:
            scores = [data['similarity_score'] for data in similarity_data.values()]
            summary['similarity_insights'] = {
                'average_similarity': np.mean(scores),
                'max_similarity': max(scores),
                'min_similarity': min(scores),
                'std_similarity': np.std(scores)
            }

            # Find most and least similar pairs
            sorted_pairs = sorted(similarity_data.items(), key=lambda x: x[1]['similarity_score'])
            summary['similarity_insights']['most_similar_pair'] = {
                'pair': sorted_pairs[-1][0],
                'score': sorted_pairs[-1][1]['similarity_score']
            }
            summary['similarity_insights']['least_similar_pair'] = {
                'pair': sorted_pairs[0][0],
                'score': sorted_pairs[0][1]['similarity_score']
            }

        # Key findings
        if 'Sim2Real_30k' in self.report_data.get('dataset_info', {}):
            sim2real_pairs = [k for k in similarity_data.keys() if 'Sim2Real_30k' in k]
            if sim2real_pairs:
                sim2real_scores = [similarity_data[k]['similarity_score'] for k in sim2real_pairs]
                avg_sim2real_similarity = np.mean(sim2real_scores)
                summary['key_findings'].append(f"Sim2Real_30k average similarity with other datasets: {avg_sim2real_similarity:.3f}")

        # Save summary
        summary_file = self.output_dir / "cross_dataset_summary_statistics.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)

        print(f"✅ Summary statistics saved: {summary_file}")


def main():
    parser = argparse.ArgumentParser(description='Generate Cross-Dataset Analysis Report')
    parser.add_argument('analysis_dir', help='Directory containing cross-dataset analysis results')
    parser.add_argument('-o', '--output', default='cross_dataset_report', help='Output directory for report')

    args = parser.parse_args()

    try:
        # Create report generator
        generator = CrossDatasetReportGenerator(args.analysis_dir, args.output)

        # Generate comprehensive report
        report_file = generator.generate_comprehensive_report()

        if report_file:
            print(f"\n🎉 Cross-dataset analysis report completed!")
            print(f"📁 Output directory: {args.output}")
            print(f"📊 Generated files:")
            print(f"  - cross_dataset_comprehensive_report.html")
            print(f"  - cross_dataset_summary_statistics.json")
            print(f"🔗 Open report: file://{report_file.absolute()}")

        return 0

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
