#!/usr/bin/env python3
"""
数据集合并工具
将训练集和验证集合并回完整数据集
"""

import json
import argparse
from pathlib import Path
from typing import Dict, List

class DatasetMerger:
    def __init__(self):
        pass
    
    def load_annotation_file(self, file_path: str) -> Dict:
        """加载标注文件"""
        print(f"加载标注文件: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✓ 文件加载完成: {len(data['images'])} 张图像, {len(data['annotations'])} 个标注")
        return data
    
    def merge_datasets(self, train_file: str, val_file: str, output_file: str):
        """合并训练集和验证集"""
        print("="*80)
        print("数据集合并工具")
        print("="*80)
        
        # 加载训练集和验证集
        train_data = self.load_annotation_file(train_file)
        val_data = self.load_annotation_file(val_file)
        
        # 验证数据格式一致性
        if train_data['categories'] != val_data['categories']:
            print("⚠ 警告: 训练集和验证集的类别定义不一致")
        
        # 创建合并后的数据集
        merged_data = {
            "info": train_data["info"].copy(),
            "licenses": train_data["licenses"],
            "categories": train_data["categories"],
            "images": [],
            "annotations": []
        }
        
        # 更新描述
        merged_data["info"]["description"] = merged_data["info"]["description"].replace(" - 训练集", "")
        merged_data["info"]["description"] += " - 完整数据集（从训练集和验证集合并）"
        
        # 合并图像信息
        merged_data["images"].extend(train_data["images"])
        merged_data["images"].extend(val_data["images"])
        
        # 合并标注信息
        merged_data["annotations"].extend(train_data["annotations"])
        merged_data["annotations"].extend(val_data["annotations"])
        
        # 检查ID冲突
        image_ids = [img["id"] for img in merged_data["images"]]
        annotation_ids = [ann["id"] for ann in merged_data["annotations"]]
        
        if len(set(image_ids)) != len(image_ids):
            print("❌ 错误: 发现重复的图像ID")
            return False
        
        if len(set(annotation_ids)) != len(annotation_ids):
            print("❌ 错误: 发现重复的标注ID")
            return False
        
        # 保存合并后的文件
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(merged_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n✓ 数据集合并完成:")
        print(f"  - 训练集: {len(train_data['images'])} 张图像, {len(train_data['annotations'])} 个标注")
        print(f"  - 验证集: {len(val_data['images'])} 张图像, {len(val_data['annotations'])} 个标注")
        print(f"  - 合并后: {len(merged_data['images'])} 张图像, {len(merged_data['annotations'])} 个标注")
        print(f"  - 输出文件: {output_path}")
        
        return True

def main():
    parser = argparse.ArgumentParser(description="数据集合并工具")
    parser.add_argument('train_file', help='训练集标注文件路径')
    parser.add_argument('val_file', help='验证集标注文件路径')
    parser.add_argument('-o', '--output', required=True, help='输出文件路径')
    
    args = parser.parse_args()
    
    # 验证输入文件
    if not Path(args.train_file).exists():
        print(f"❌ 训练集文件不存在: {args.train_file}")
        return 1
    
    if not Path(args.val_file).exists():
        print(f"❌ 验证集文件不存在: {args.val_file}")
        return 1
    
    # 执行合并
    merger = DatasetMerger()
    success = merger.merge_datasets(args.train_file, args.val_file, args.output)
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
