#!/usr/bin/env python3
"""
Interactive Dataset Visualization Dashboard
Comprehensive visualization interface for Sim2Real_30k dataset
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import argparse
from typing import Dict, List, Optional
import webbrowser
import base64
from io import BytesIO

# Set style
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
sns.set_palette("husl")

class InteractiveDashboard:
    def __init__(self, dataset_root: str, output_dir: str):
        self.dataset_root = Path(dataset_root)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Load all available data
        self.annotation_data = self.load_annotation_data()
        self.domain_analysis = self.load_domain_analysis()
        self.visualization_files = self.find_visualization_files()
        
    def load_annotation_data(self) -> Optional[Dict]:
        """Load COCO annotation data"""
        annotation_file = self.dataset_root / "annotations" / "instances_complete.json"
        if annotation_file.exists():
            with open(annotation_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None
    
    def load_domain_analysis(self) -> Optional[Dict]:
        """Load domain gap analysis results"""
        domain_file = self.dataset_root / "domain_analysis" / "domain_gap_report.json"
        if domain_file.exists():
            with open(domain_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None
    
    def find_visualization_files(self) -> Dict[str, Path]:
        """Find existing visualization files"""
        files = {}
        
        # Check visualization_en directory
        viz_dir = self.dataset_root / "visualization_en"
        if viz_dir.exists():
            for file in viz_dir.glob("*.png"):
                files[file.stem] = file
        
        # Check domain_analysis directory
        domain_dir = self.dataset_root / "domain_analysis"
        if domain_dir.exists():
            for file in domain_dir.glob("*.png"):
                files[f"domain_{file.stem}"] = file
                
        return files
    
    def encode_image_to_base64(self, image_path: Path) -> str:
        """Convert image to base64 for HTML embedding"""
        with open(image_path, 'rb') as f:
            image_data = f.read()
        return base64.b64encode(image_data).decode('utf-8')
    
    def generate_dataset_summary(self) -> Dict:
        """Generate comprehensive dataset summary"""
        if not self.annotation_data:
            return {}
        
        images = self.annotation_data['images']
        annotations = self.annotation_data['annotations']
        
        # Extract batch information
        batch_stats = {}
        for img in images:
            batch_id = img['file_name'].split('_')[0]
            if batch_id not in batch_stats:
                batch_stats[batch_id] = {'images': 0, 'annotations': 0}
            batch_stats[batch_id]['images'] += 1
        
        for ann in annotations:
            img_id = ann['image_id']
            img_info = next(img for img in images if img['id'] == img_id)
            batch_id = img_info['file_name'].split('_')[0]
            batch_stats[batch_id]['annotations'] += 1
        
        # Calculate target sizes
        target_sizes = []
        for ann in annotations:
            bbox = ann['bbox']
            size = max(bbox[2], bbox[3])  # max of width, height
            target_sizes.append(size)
        
        summary = {
            'total_images': len(images),
            'total_annotations': len(annotations),
            'total_batches': len(batch_stats),
            'avg_targets_per_image': len(annotations) / len(images),
            'target_size_stats': {
                'min': min(target_sizes),
                'max': max(target_sizes),
                'mean': np.mean(target_sizes),
                'std': np.std(target_sizes)
            },
            'batch_stats': batch_stats
        }
        
        return summary
    
    def create_html_dashboard(self) -> str:
        """Create comprehensive HTML dashboard"""
        summary = self.generate_dataset_summary()
        
        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sim2Real_30k Dataset Dashboard</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #3498db;
        }}
        .header h1 {{
            color: #2c3e50;
            margin-bottom: 10px;
        }}
        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }}
        .stat-card {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        .stat-value {{
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        .stat-label {{
            font-size: 1.1em;
            opacity: 0.9;
        }}
        .visualization-section {{
            margin-bottom: 40px;
        }}
        .section-title {{
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }}
        .viz-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 20px;
        }}
        .viz-card {{
            background: white;
            border: 1px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
        .viz-card img {{
            width: 100%;
            height: auto;
            display: block;
        }}
        .viz-card-title {{
            padding: 15px;
            background-color: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }}
        .domain-analysis {{
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }}
        .analysis-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }}
        .analysis-item {{
            background: rgba(255,255,255,0.8);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }}
        .footer {{
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 Sim2Real_30k Dataset Dashboard</h1>
            <p>Comprehensive Analysis and Visualization Report</p>
        </div>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value">{summary.get('total_images', 'N/A'):,}</div>
                <div class="stat-label">Total Images</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{summary.get('total_annotations', 'N/A'):,}</div>
                <div class="stat-label">Total Annotations</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{summary.get('total_batches', 'N/A')}</div>
                <div class="stat-label">Total Batches</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">{summary.get('avg_targets_per_image', 0):.2f}</div>
                <div class="stat-label">Avg Targets/Image</div>
            </div>
        </div>
"""
        
        return html_content
    
    def generate_dashboard(self):
        """Generate complete interactive dashboard"""
        print("🚀 Generating interactive dashboard...")
        
        # Create HTML dashboard
        html_content = self.create_html_dashboard()
        
        # Add visualization sections
        html_content += self.add_visualization_sections()
        
        # Add domain analysis section
        if self.domain_analysis:
            html_content += self.add_domain_analysis_section()
        
        # Close HTML
        html_content += """
        <div class="footer">
            <p>Generated by Sim2Real Dataset Visualization Tools</p>
            <p>For more information, visit the project repository</p>
        </div>
    </div>
</body>
</html>"""
        
        # Save dashboard
        dashboard_file = self.output_dir / "interactive_dashboard.html"
        with open(dashboard_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✓ Dashboard saved: {dashboard_file}")
        return dashboard_file

    def add_visualization_sections(self) -> str:
        """Add visualization sections to HTML"""
        html = """
        <div class="visualization-section">
            <h2 class="section-title">📊 Dataset Visualizations</h2>
            <div class="viz-grid">
"""

        # Add available visualizations
        viz_titles = {
            'size_distribution_en': 'Target Size Distribution Analysis',
            'batch_comparison_en': 'Inter-Batch Comparison Analysis',
            'domain_domain_gap_analysis': 'Domain Gap Analysis Visualization'
        }

        for viz_key, viz_path in self.visualization_files.items():
            title = viz_titles.get(viz_key, viz_key.replace('_', ' ').title())
            if viz_path.exists():
                img_base64 = self.encode_image_to_base64(viz_path)
                html += f"""
                <div class="viz-card">
                    <div class="viz-card-title">{title}</div>
                    <img src="data:image/png;base64,{img_base64}" alt="{title}">
                </div>
"""

        html += """
            </div>
        </div>
"""
        return html

    def add_domain_analysis_section(self) -> str:
        """Add domain analysis section"""
        if not self.domain_analysis:
            return ""

        analysis = self.domain_analysis

        html = f"""
        <div class="visualization-section">
            <h2 class="section-title">🧠 Domain Gap Analysis</h2>
            <div class="domain-analysis">
                <h3>Deep Learning-based Domain Diversity Analysis</h3>
                <p>Using UNet feature extraction to analyze domain gaps between batches</p>

                <div class="analysis-grid">
                    <div class="analysis-item">
                        <h4>Similarity Score</h4>
                        <div class="stat-value">{analysis.get('average_similarity', 0):.3f}</div>
                    </div>
                    <div class="analysis-item">
                        <h4>Diversity Index</h4>
                        <div class="stat-value">{analysis.get('diversity_score', 0):.3f}</div>
                    </div>
                    <div class="analysis-item">
                        <h4>Clusters Found</h4>
                        <div class="stat-value">{analysis.get('optimal_clusters', 'N/A')}</div>
                    </div>
                    <div class="analysis-item">
                        <h4>Analysis Method</h4>
                        <div class="stat-value">UNet + PCA</div>
                    </div>
                </div>

                <div style="margin-top: 20px;">
                    <h4>Recommendations:</h4>
                    <ul>
"""

        # Add recommendations based on analysis
        if analysis.get('diversity_score', 0) > 0.7:
            html += "<li>✅ High domain diversity detected - excellent for model generalization</li>"
        elif analysis.get('diversity_score', 0) > 0.4:
            html += "<li>⚠️ Moderate domain diversity - consider domain adaptation techniques</li>"
        else:
            html += "<li>❌ Low domain diversity - implement progressive training strategies</li>"

        html += """
                    <li>🔄 Use batch-aware sampling during training</li>
                    <li>📈 Monitor performance across different domain clusters</li>
                </ul>
                </div>
            </div>
        </div>
"""
        return html


def main():
    parser = argparse.ArgumentParser(description='Generate interactive dataset dashboard')
    parser.add_argument('dataset_root', help='Root directory of the dataset')
    parser.add_argument('-o', '--output', default='dashboard_output', help='Output directory')

    args = parser.parse_args()

    # Create dashboard
    dashboard = InteractiveDashboard(args.dataset_root, args.output)
    dashboard_file = dashboard.generate_dashboard()

    # Open in browser
    print(f"\n🌐 Opening dashboard in browser...")
    webbrowser.open(f'file://{dashboard_file.absolute()}')

    print(f"\n✅ Interactive dashboard generated successfully!")
    print(f"📁 Output: {dashboard_file}")
    print(f"🔗 URL: file://{dashboard_file.absolute()}")

if __name__ == "__main__":
    main()
