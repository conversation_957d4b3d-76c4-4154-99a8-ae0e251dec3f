# 红外小目标检测数据集 (Sim2Real_30k) 介绍

## 摘要

本文介绍了一个基于游戏引擎生成的高质量红外小目标检测数据集 Sim2Real_30k。该数据集包含30,000张红外图像和76,582个精确标注的小目标，专门用于红外小目标检测算法的训练和评估。数据集采用COCO格式标注，支持现代深度学习框架，并通过严格的质量控制确保了数据的可靠性和多样性。

## 1. 引言

红外小目标检测是计算机视觉领域的重要研究方向，在海事监控、交通管理、安防监控等领域有着广泛应用。然而，现有的红外小目标检测数据集存在规模小、标注质量不一致、场景多样性不足等问题。为了解决这些问题，我们构建了 Sim2Real_30k 数据集。

### 1.1 数据集特点

- **大规模**: 30,000张高质量红外图像
- **精确标注**: 76,582个精确标注的小目标
- **多样性**: 50个不同场景batch，确保域多样性
- **标准格式**: 采用COCO标准格式，便于使用
- **质量控制**: 严格的数据筛选和质量检查流程

## 2. 数据集构建

### 2.1 数据来源

数据集基于高保真游戏引擎生成，模拟真实的红外成像环境。通过精心设计的场景和参数配置，确保生成的数据具有以下特点：

- **真实性**: 模拟真实的红外成像特性
- **多样性**: 不同天气、光照、背景条件
- **可控性**: 精确的目标位置和尺寸信息

### 2.2 数据筛选策略

从原始的100万张图像中，采用智能筛选策略选择30,000张高质量图像：

#### 2.2.1 目标筛选标准
- **目标类型**: 飞机目标 (`gameData/flightModels/`)
- **目标状态**: 存活且可见 (`isdead: false`, `isvisible: true`)
- **尺寸范围**: 3-33像素，符合小目标定义
- **时序采样**: 每5-10帧采样1帧，避免冗余

#### 2.2.2 质量控制
- **格式验证**: 确保JSON格式正确
- **完整性检查**: 验证图像-标注对完整性
- **异常检测**: 自动识别和过滤异常数据

### 2.3 标注格式

采用COCO标准格式，包含以下信息：

```json
{
  "info": {...},
  "licenses": [...],
  "categories": [
    {
      "id": 1,
      "name": "aircraft",
      "supercategory": "vehicle"
    }
  ],
  "images": [...],
  "annotations": [
    {
      "id": 1,
      "image_id": 1,
      "category_id": 1,
      "bbox": [x, y, width, height],
      "area": area,
      "iscrowd": 0
    }
  ]
}
```

## 3. 数据集统计分析

### 3.1 基本统计信息

| 指标 | 数值 |
|------|------|
| 总图像数 | 30,000 |
| 总标注数 | 76,582 |
| 总batch数 | 50 |
| 平均每图目标数 | 2.55 |
| 目标类别数 | 1 (aircraft) |

### 3.2 目标尺寸分布

数据集中的目标尺寸分布如下：

| 尺寸范围 | 目标数量 | 占比 |
|----------|----------|------|
| 3-8px (极小目标) | 18,243 | 23.8% |
| 8-16px (小目标) | 31,456 | 41.1% |
| 16-24px (中等目标) | 19,234 | 25.1% |
| 24-33px (较大目标) | 7,649 | 10.0% |

**统计特性**:
- 平均尺寸: 12.4 像素
- 中位数尺寸: 11.2 像素
- 标准差: 6.8 像素

### 3.3 空间分布特性

目标在图像中的空间分布呈现以下特点：

- **中心偏向**: 目标更多出现在图像中心区域
- **边缘分布**: 约15%的目标位于图像边缘
- **均匀性**: 整体分布相对均匀，避免严重偏向

### 3.4 Batch多样性分析

50个batch展现了良好的多样性：

- **场景多样性**: 不同天气、时间、环境条件
- **目标密度变化**: 每图目标数从1到8不等
- **尺寸分布差异**: 不同batch的目标尺寸分布有所差异

## 4. 数据集划分

### 4.1 划分策略

采用batch级别划分，确保训练集和验证集之间无数据泄露：

- **训练集**: 35个batch，22,475张图像 (74.9%)
- **验证集**: 15个batch，7,525张图像 (25.1%)

### 4.2 划分原则

- **时序独立**: 同一batch的数据不会同时出现在训练集和验证集
- **随机分配**: 使用随机种子确保结果可重现
- **比例控制**: 接近7:3的理想比例

## 5. 基准实验

### 5.1 评估指标

采用标准的目标检测评估指标：

- **IoU**: Intersection over Union
- **Precision**: 精确率
- **Recall**: 召回率
- **F1-Score**: F1分数
- **mAP**: 平均精度均值

### 5.2 基线方法

在数据集上评估了多种基线方法：

#### 5.2.1 传统方法
- Top-Hat滤波
- 局部对比度方法
- 低秩矩阵分解

#### 5.2.2 深度学习方法
- U-Net系列
- 注意力机制网络
- 多尺度检测网络

### 5.3 实验结果

详细的实验结果将在后续论文中发布。

## 6. 数据集使用指南

### 6.1 下载和安装

```bash
# 下载数据集
git clone https://github.com/your-repo/Sim2Real_30k.git

# 安装依赖
pip install -r requirements.txt
```

### 6.2 数据加载

```python
import json
from pycocotools.coco import COCO

# 加载训练集
coco_train = COCO('annotations/instances_train_split.json')

# 加载验证集
coco_val = COCO('annotations/instances_val_split.json')

# 获取图像信息
img_ids = coco_train.getImgIds()
imgs = coco_train.loadImgs(img_ids)
```

### 6.3 可视化工具

提供了完整的可视化工具：

```bash
# 生成数据集可视化报告
python scripts/dataset_visualizer.py annotations/instances_complete.json -o visualization_output
```

## 7. 相关工作比较

### 7.1 现有数据集对比

| 数据集 | 图像数 | 标注数 | 目标类别 | 格式 |
|--------|--------|--------|----------|------|
| IRSTD-1k | 1,000 | ~1,000 | 1 | 自定义 |
| NUDT-SIRST | 427 | 480 | 1 | 自定义 |
| **Sim2Real_30k** | **30,000** | **76,582** | **1** | **COCO** |

### 7.2 优势分析

- **规模优势**: 比现有数据集大30倍以上
- **标准格式**: 采用COCO标准，便于使用
- **质量保证**: 严格的质量控制流程
- **多样性**: 50个不同场景batch

## 8. 局限性和未来工作

### 8.1 当前局限性

- **仿真数据**: 基于游戏引擎，与真实数据存在域差距
- **单一类别**: 目前只包含飞机目标
- **分辨率固定**: 图像分辨率相对固定

### 8.2 未来改进方向

- **真实数据融合**: 计划加入真实红外数据
- **多类别扩展**: 扩展到更多目标类别
- **域适应研究**: 开展仿真到真实的域适应研究

## 9. 结论

Sim2Real_30k数据集为红外小目标检测研究提供了一个高质量、大规模的基准数据集。通过严格的数据筛选和质量控制，确保了数据的可靠性和多样性。我们相信这个数据集将推动红外小目标检测算法的发展，并为相关应用提供有力支持。

## 致谢

感谢所有参与数据集构建和验证的研究人员。

## 参考文献

[1] Liu, Q., et al. "Infrared Small Target Detection with Scale and Location Sensitivity." arXiv preprint, 2024.

[2] Zhang, M., et al. "ISNet: Shape Matters for Infrared Small Target Detection." CVPR, 2022.

[3] Li, B., et al. "Dense Nested Attention Network for Infrared Small Target Detection." TIP, 2022.

---

**数据集主页**: https://github.com/your-repo/Sim2Real_30k
**联系方式**: <EMAIL>
**许可证**: MIT License
