#!/usr/bin/env python3
"""
Sim2Real_30k Dataset Split Code
可直接复制使用的数据集划分代码，确保每次划分结果一致
"""

import json
import random
from pathlib import Path
from collections import defaultdict

def split_sim2real_30k_dataset():
    """
    Sim2Real_30k数据集标准划分方法
    按批次划分，确保同一批次的数据不会同时出现在训练集和验证集中
    训练集：70%的批次，验证集：30%的批次
    """
    
    # 设置随机种子，确保每次划分结果一致
    random.seed(42)
    
    # 输入和输出路径
    input_annotation = "annotations/instances_complete.json"
    output_dir = "annotations"
    
    # 创建输出目录
    Path(output_dir).mkdir(parents=True, exist_ok=True)
    
    print("🚀 开始划分Sim2Real_30k数据集...")
    
    # 1. 加载完整标注文件
    print("📂 加载标注文件...")
    with open(input_annotation, 'r', encoding='utf-8') as f:
        coco_data = json.load(f)
    
    print(f"✓ 总图像数: {len(coco_data['images']):,}")
    print(f"✓ 总标注数: {len(coco_data['annotations']):,}")
    
    # 2. 按批次组织图像
    print("📊 按批次组织数据...")
    batch_images = defaultdict(list)
    
    for img in coco_data['images']:
        # 从文件名提取批次ID (例如: batch10_005510.png -> batch10)
        batch_id = img['file_name'].split('_')[0]
        batch_images[batch_id].append(img)
    
    batch_ids = sorted(batch_images.keys())
    print(f"✓ 发现 {len(batch_ids)} 个批次: {batch_ids[:5]}...{batch_ids[-5:]}")
    
    # 3. 划分批次 (70% 训练, 30% 验证)
    print("🎯 划分批次...")
    random.shuffle(batch_ids)  # 随机打乱批次顺序
    
    split_point = int(len(batch_ids) * 0.7)
    train_batches = set(batch_ids[:split_point])
    val_batches = set(batch_ids[split_point:])
    
    print(f"✓ 训练批次: {len(train_batches)} 个")
    print(f"✓ 验证批次: {len(val_batches)} 个")
    
    # 4. 分配图像到训练集和验证集
    print("📋 分配图像...")
    train_images = []
    val_images = []
    
    for img in coco_data['images']:
        batch_id = img['file_name'].split('_')[0]
        if batch_id in train_batches:
            train_images.append(img)
        else:
            val_images.append(img)
    
    print(f"✓ 训练图像: {len(train_images):,} 张")
    print(f"✓ 验证图像: {len(val_images):,} 张")
    
    # 5. 分配标注
    print("🎯 分配标注...")
    train_image_ids = {img['id'] for img in train_images}
    val_image_ids = {img['id'] for img in val_images}
    
    train_annotations = []
    val_annotations = []
    
    for ann in coco_data['annotations']:
        if ann['image_id'] in train_image_ids:
            train_annotations.append(ann)
        elif ann['image_id'] in val_image_ids:
            val_annotations.append(ann)
    
    print(f"✓ 训练标注: {len(train_annotations):,} 个")
    print(f"✓ 验证标注: {len(val_annotations):,} 个")
    
    # 6. 创建训练集COCO文件
    print("💾 保存训练集...")
    train_coco = {
        'info': coco_data.get('info', {}),
        'licenses': coco_data.get('licenses', []),
        'categories': coco_data['categories'],
        'images': train_images,
        'annotations': train_annotations
    }
    
    train_file = Path(output_dir) / "instances_train_split.json"
    with open(train_file, 'w', encoding='utf-8') as f:
        json.dump(train_coco, f, ensure_ascii=False, indent=2)
    
    # 7. 创建验证集COCO文件
    print("💾 保存验证集...")
    val_coco = {
        'info': coco_data.get('info', {}),
        'licenses': coco_data.get('licenses', []),
        'categories': coco_data['categories'],
        'images': val_images,
        'annotations': val_annotations
    }
    
    val_file = Path(output_dir) / "instances_val_split.json"
    with open(val_file, 'w', encoding='utf-8') as f:
        json.dump(val_coco, f, ensure_ascii=False, indent=2)
    
    # 8. 生成划分报告
    print("📊 生成划分报告...")
    split_report = {
        'split_method': 'batch_based',
        'train_ratio': 0.7,
        'val_ratio': 0.3,
        'random_seed': 42,
        'total_batches': len(batch_ids),
        'train_batches': len(train_batches),
        'val_batches': len(val_batches),
        'train_batch_ids': sorted(list(train_batches)),
        'val_batch_ids': sorted(list(val_batches)),
        'statistics': {
            'total_images': len(coco_data['images']),
            'total_annotations': len(coco_data['annotations']),
            'train_images': len(train_images),
            'train_annotations': len(train_annotations),
            'val_images': len(val_images),
            'val_annotations': len(val_annotations),
            'train_images_ratio': len(train_images) / len(coco_data['images']),
            'train_annotations_ratio': len(train_annotations) / len(coco_data['annotations'])
        }
    }
    
    report_file = Path(output_dir) / "split_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(split_report, f, ensure_ascii=False, indent=2)
    
    # 9. 打印最终统计
    print("\n" + "="*50)
    print("✅ 数据集划分完成!")
    print("="*50)
    print(f"📁 输出文件:")
    print(f"  - {train_file}")
    print(f"  - {val_file}")
    print(f"  - {report_file}")
    print(f"\n📊 划分统计:")
    print(f"  训练集: {len(train_images):,} 图像, {len(train_annotations):,} 标注 ({len(train_images)/len(coco_data['images'])*100:.1f}%)")
    print(f"  验证集: {len(val_images):,} 图像, {len(val_annotations):,} 标注 ({len(val_images)/len(coco_data['images'])*100:.1f}%)")
    print(f"\n🎯 批次分布:")
    print(f"  训练批次: {sorted(list(train_batches))[:3]}...{sorted(list(train_batches))[-3:]}")
    print(f"  验证批次: {sorted(list(val_batches))[:3]}...{sorted(list(val_batches))[-3:]}")
    
    return train_file, val_file, report_file


if __name__ == "__main__":
    # 直接运行划分
    split_sim2real_30k_dataset()
    
    print("\n" + "="*50)
    print("💡 使用说明:")
    print("="*50)
    print("1. 将此代码保存为 dataset_split.py")
    print("2. 确保 annotations/instances_complete.json 存在")
    print("3. 运行: python dataset_split.py")
    print("4. 生成的文件:")
    print("   - annotations/instances_train_split.json (训练集)")
    print("   - annotations/instances_val_split.json (验证集)")
    print("   - annotations/split_report.json (划分报告)")
    print("\n🔄 重现性保证:")
    print("- 使用固定随机种子 (seed=42)")
    print("- 按批次划分，避免数据泄露")
    print("- 每次运行结果完全一致")
