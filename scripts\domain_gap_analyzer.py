#!/usr/bin/env python3
"""
域差距分析工具
使用深度学习网络提取特征，分析不同batch间的域差距
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Tuple, Optional
import argparse

try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    from torchvision import transforms
    from PIL import Image
    TORCH_AVAILABLE = True
except ImportError:
    print("警告: PyTorch未安装，将使用传统特征提取方法")
    TORCH_AVAILABLE = False

try:
    from sklearn.manifold import TSNE
    from sklearn.decomposition import PCA
    from sklearn.cluster import KMeans
    SKLEARN_AVAILABLE = True
except ImportError:
    print("警告: scikit-learn未安装，部分功能将不可用")
    SKLEARN_AVAILABLE = False

# 设置字体
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

class SimpleUNet(nn.Module):
    """简化的UNet用于特征提取"""
    def __init__(self, in_channels=1, out_channels=64):
        super(SimpleUNet, self).__init__()
        
        # Encoder
        self.enc1 = self.conv_block(in_channels, 64)
        self.enc2 = self.conv_block(64, 128)
        self.enc3 = self.conv_block(128, 256)
        self.enc4 = self.conv_block(256, 512)
        
        # Decoder
        self.dec3 = self.conv_block(512 + 256, 256)
        self.dec2 = self.conv_block(256 + 128, 128)
        self.dec1 = self.conv_block(128 + 64, 64)
        
        # Feature output
        self.feature_conv = nn.Conv2d(64, out_channels, 1)
        
        self.pool = nn.MaxPool2d(2)
        self.upsample = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
        
    def conv_block(self, in_ch, out_ch):
        return nn.Sequential(
            nn.Conv2d(in_ch, out_ch, 3, padding=1),
            nn.BatchNorm2d(out_ch),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_ch, out_ch, 3, padding=1),
            nn.BatchNorm2d(out_ch),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x):
        # Encoder
        e1 = self.enc1(x)
        e2 = self.enc2(self.pool(e1))
        e3 = self.enc3(self.pool(e2))
        e4 = self.enc4(self.pool(e3))
        
        # Decoder
        d3 = self.dec3(torch.cat([self.upsample(e4), e3], dim=1))
        d2 = self.dec2(torch.cat([self.upsample(d3), e2], dim=1))
        d1 = self.dec1(torch.cat([self.upsample(d2), e1], dim=1))
        
        # Feature map
        features = self.feature_conv(d1)
        
        return features, e4  # 返回特征图和编码器最深层特征

class DomainGapAnalyzer:
    def __init__(self, annotation_file: str, image_dir: str, output_dir: str):
        self.annotation_file = annotation_file
        self.image_dir = Path(image_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载数据
        self.coco_data = self.load_coco_data()
        self.batch_images = self.organize_by_batch()
        
        # 初始化网络
        if TORCH_AVAILABLE:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            self.model = SimpleUNet(in_channels=1, out_channels=64).to(self.device)
            self.model.eval()
            print(f"使用设备: {self.device}")
        
        # 图像预处理
        self.transform = transforms.Compose([
            transforms.Grayscale(),
            transforms.Resize((256, 256)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.5], std=[0.5])
        ]) if TORCH_AVAILABLE else None
    
    def load_coco_data(self) -> Dict:
        """加载COCO格式数据"""
        print(f"Loading dataset: {self.annotation_file}")
        with open(self.annotation_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✓ Dataset loaded: {len(data['images'])} images, {len(data['annotations'])} annotations")
        return data
    
    def organize_by_batch(self) -> Dict[str, List[Dict]]:
        """按batch组织图像"""
        batch_images = defaultdict(list)
        
        for image in self.coco_data['images']:
            filename = image['file_name']
            if filename.startswith('batch'):
                batch_id = filename.split('_')[0]
                batch_images[batch_id].append(image)
        
        print(f"✓ Organized into {len(batch_images)} batches")
        return dict(batch_images)
    
    def load_and_preprocess_image(self, image_path: Path) -> Optional[torch.Tensor]:
        """加载和预处理图像"""
        try:
            if not image_path.exists():
                return None
            
            image = Image.open(image_path).convert('RGB')
            if self.transform:
                image_tensor = self.transform(image).unsqueeze(0)
                return image_tensor.to(self.device)
            else:
                # 如果没有torch，返回numpy数组
                image_gray = image.convert('L')
                image_array = np.array(image_gray).astype(np.float32) / 255.0
                return image_array
        except Exception as e:
            print(f"Error loading image {image_path}: {e}")
            return None
    
    def extract_features_with_network(self, batch_id: str, max_images: int = 20) -> Optional[np.ndarray]:
        """使用网络提取特征"""
        if not TORCH_AVAILABLE:
            return self.extract_traditional_features(batch_id, max_images)
        
        batch_images = self.batch_images.get(batch_id, [])
        if not batch_images:
            return None
        
        # 限制图像数量以节省计算资源
        selected_images = batch_images[:max_images]
        features_list = []
        
        print(f"Extracting features for {batch_id} ({len(selected_images)} images)...")
        
        with torch.no_grad():
            for img_info in selected_images:
                img_path = self.image_dir / img_info['file_name']
                img_tensor = self.load_and_preprocess_image(img_path)
                
                if img_tensor is not None:
                    # 提取特征
                    feature_map, deep_features = self.model(img_tensor)
                    
                    # 全局平均池化得到特征向量
                    global_features = F.adaptive_avg_pool2d(deep_features, (1, 1))
                    global_features = global_features.view(global_features.size(0), -1)
                    
                    features_list.append(global_features.cpu().numpy())
        
        if features_list:
            # 计算batch的平均特征
            batch_features = np.mean(np.vstack(features_list), axis=0)
            return batch_features
        
        return None
    
    def extract_traditional_features(self, batch_id: str, max_images: int = 20) -> Optional[np.ndarray]:
        """传统特征提取方法（当PyTorch不可用时）"""
        batch_images = self.batch_images.get(batch_id, [])
        if not batch_images:
            return None
        
        selected_images = batch_images[:max_images]
        features_list = []
        
        print(f"Extracting traditional features for {batch_id} ({len(selected_images)} images)...")
        
        for img_info in selected_images:
            img_path = self.image_dir / img_info['file_name']
            img_array = self.load_and_preprocess_image(img_path)
            
            if img_array is not None:
                # 提取简单的统计特征
                features = [
                    np.mean(img_array),           # 平均亮度
                    np.std(img_array),            # 亮度标准差
                    np.percentile(img_array, 25), # 25%分位数
                    np.percentile(img_array, 75), # 75%分位数
                    np.mean(np.gradient(img_array)[0]),  # 水平梯度均值
                    np.mean(np.gradient(img_array)[1]),  # 垂直梯度均值
                ]
                features_list.append(features)
        
        if features_list:
            batch_features = np.mean(features_list, axis=0)
            return batch_features
        
        return None
    
    def analyze_domain_gap(self):
        """分析域差距"""
        print("Starting domain gap analysis...")
        
        # 提取所有batch的特征
        batch_features = {}
        batch_names = sorted(self.batch_images.keys(), key=lambda x: int(x.replace('batch', '')))
        
        for batch_id in batch_names:
            features = self.extract_features_with_network(batch_id)
            if features is not None:
                batch_features[batch_id] = features
        
        if not batch_features:
            print("❌ No features extracted")
            return
        
        print(f"✓ Extracted features for {len(batch_features)} batches")
        
        # 转换为矩阵
        feature_matrix = np.vstack(list(batch_features.values()))
        batch_labels = list(batch_features.keys())
        
        # 可视化分析
        self.visualize_domain_gap(feature_matrix, batch_labels)
        
        return batch_features
    
    def visualize_domain_gap(self, feature_matrix: np.ndarray, batch_labels: List[str]):
        """可视化域差距"""
        print("Generating domain gap visualizations...")
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Domain Gap Analysis Between Batches', fontsize=16, fontweight='bold')
        
        # 1. 特征相似性热力图
        similarity_matrix = np.corrcoef(feature_matrix)
        
        im1 = axes[0, 0].imshow(similarity_matrix, cmap='coolwarm', vmin=-1, vmax=1)
        axes[0, 0].set_title('Batch Feature Similarity Matrix')
        axes[0, 0].set_xticks(range(len(batch_labels)))
        axes[0, 0].set_yticks(range(len(batch_labels)))
        axes[0, 0].set_xticklabels([f'B{i+1}' for i in range(len(batch_labels))], rotation=45)
        axes[0, 0].set_yticklabels([f'B{i+1}' for i in range(len(batch_labels))])
        plt.colorbar(im1, ax=axes[0, 0], label='Correlation')
        
        # 2. PCA降维可视化
        if SKLEARN_AVAILABLE and feature_matrix.shape[1] > 2:
            pca = PCA(n_components=2)
            pca_features = pca.fit_transform(feature_matrix)
            
            scatter = axes[0, 1].scatter(pca_features[:, 0], pca_features[:, 1], 
                                       c=range(len(batch_labels)), cmap='tab20', s=100)
            axes[0, 1].set_title(f'PCA Visualization (Explained Variance: {pca.explained_variance_ratio_.sum():.2f})')
            axes[0, 1].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2f})')
            axes[0, 1].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2f})')
            
            # 添加标签
            for i, label in enumerate(batch_labels):
                axes[0, 1].annotate(f'B{i+1}', (pca_features[i, 0], pca_features[i, 1]), 
                                  xytext=(5, 5), textcoords='offset points')
        else:
            axes[0, 1].text(0.5, 0.5, 'PCA not available\n(install scikit-learn)', 
                          ha='center', va='center', transform=axes[0, 1].transAxes)
            axes[0, 1].set_title('PCA Visualization')
        
        # 3. 特征距离分析
        distances = []
        for i in range(len(feature_matrix)):
            for j in range(i+1, len(feature_matrix)):
                dist = np.linalg.norm(feature_matrix[i] - feature_matrix[j])
                distances.append(dist)
        
        axes[1, 0].hist(distances, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        axes[1, 0].set_title('Distribution of Pairwise Distances')
        axes[1, 0].set_xlabel('Euclidean Distance')
        axes[1, 0].set_ylabel('Frequency')
        axes[1, 0].axvline(np.mean(distances), color='red', linestyle='--', 
                          label=f'Mean: {np.mean(distances):.2f}')
        axes[1, 0].legend()
        
        # 4. 聚类分析
        if SKLEARN_AVAILABLE and len(feature_matrix) >= 3:
            n_clusters = min(5, len(feature_matrix))
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            cluster_labels = kmeans.fit_predict(feature_matrix)
            
            # 如果有PCA结果，在PCA空间中显示聚类
            if 'pca_features' in locals():
                scatter = axes[1, 1].scatter(pca_features[:, 0], pca_features[:, 1], 
                                           c=cluster_labels, cmap='Set1', s=100)
                axes[1, 1].set_title(f'K-Means Clustering (k={n_clusters})')
                axes[1, 1].set_xlabel('PC1')
                axes[1, 1].set_ylabel('PC2')
                
                # 添加聚类中心
                if feature_matrix.shape[1] > 2:
                    centers_pca = pca.transform(kmeans.cluster_centers_)
                    axes[1, 1].scatter(centers_pca[:, 0], centers_pca[:, 1], 
                                     c='red', marker='x', s=200, linewidths=3)
            else:
                # 显示聚类结果的条形图
                cluster_counts = np.bincount(cluster_labels)
                axes[1, 1].bar(range(n_clusters), cluster_counts, alpha=0.7)
                axes[1, 1].set_title(f'Cluster Distribution (k={n_clusters})')
                axes[1, 1].set_xlabel('Cluster ID')
                axes[1, 1].set_ylabel('Number of Batches')
        else:
            axes[1, 1].text(0.5, 0.5, 'Clustering not available', 
                          ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('Clustering Analysis')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'domain_gap_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✓ Domain gap analysis saved: {self.output_dir / 'domain_gap_analysis.png'}")
    
    def generate_report(self, batch_features: Dict[str, np.ndarray]):
        """生成域差距分析报告"""
        if not batch_features:
            return
        
        feature_matrix = np.vstack(list(batch_features.values()))
        
        # 计算统计信息
        pairwise_distances = []
        for i, (batch1, feat1) in enumerate(batch_features.items()):
            for j, (batch2, feat2) in enumerate(list(batch_features.items())[i+1:], i+1):
                dist = np.linalg.norm(feat1 - feat2)
                pairwise_distances.append({
                    'batch1': batch1,
                    'batch2': batch2,
                    'distance': float(dist)
                })
        
        report = {
            'analysis_summary': {
                'total_batches': len(batch_features),
                'feature_dimension': feature_matrix.shape[1],
                'method': 'UNet-based' if TORCH_AVAILABLE else 'Traditional',
                'device': str(self.device) if TORCH_AVAILABLE else 'CPU'
            },
            'distance_statistics': {
                'mean_distance': float(np.mean([d['distance'] for d in pairwise_distances])),
                'std_distance': float(np.std([d['distance'] for d in pairwise_distances])),
                'min_distance': float(np.min([d['distance'] for d in pairwise_distances])),
                'max_distance': float(np.max([d['distance'] for d in pairwise_distances]))
            },
            'pairwise_distances': pairwise_distances,
            'recommendations': {
                'domain_diversity': 'High' if np.std([d['distance'] for d in pairwise_distances]) > np.mean([d['distance'] for d in pairwise_distances]) * 0.3 else 'Moderate',
                'suggested_actions': [
                    'Consider domain adaptation techniques for training',
                    'Use batch-aware sampling strategies',
                    'Implement progressive training across batches'
                ]
            }
        }
        
        # 保存报告
        with open(self.output_dir / 'domain_gap_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✓ Domain gap report saved: {self.output_dir / 'domain_gap_report.json'}")
        
        return report

def main():
    parser = argparse.ArgumentParser(description="Domain Gap Analysis Tool")
    parser.add_argument('annotation_file', help='COCO annotation file path')
    parser.add_argument('image_dir', help='Directory containing images')
    parser.add_argument('-o', '--output', default='domain_analysis_output', help='Output directory')
    parser.add_argument('--max-images', type=int, default=20, help='Max images per batch to analyze')
    
    args = parser.parse_args()
    
    # 验证输入
    if not Path(args.annotation_file).exists():
        print(f"❌ Annotation file not found: {args.annotation_file}")
        return 1
    
    if not Path(args.image_dir).exists():
        print(f"❌ Image directory not found: {args.image_dir}")
        return 1
    
    # 创建分析器并运行
    analyzer = DomainGapAnalyzer(args.annotation_file, args.image_dir, args.output)
    batch_features = analyzer.analyze_domain_gap()
    
    if batch_features:
        analyzer.generate_report(batch_features)
        print("\n✓ Domain gap analysis completed successfully!")
    else:
        print("❌ Domain gap analysis failed")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
