#!/usr/bin/env python3
"""
Test cross-dataset domain analyzer
"""
import sys
import os
from pathlib import Path

# Add scripts directory to path
sys.path.append('scripts')

def test_imports():
    """Test all required imports"""
    try:
        print("Testing imports...")
        
        import numpy as np
        print("✓ NumPy available")
        
        import matplotlib.pyplot as plt
        print("✓ Matplotlib available")
        
        import cv2
        print("✓ OpenCV available")
        
        import torch
        print("✓ PyTorch available")
        
        from sklearn.decomposition import PCA
        print("✓ scikit-learn available")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_dataset_paths():
    """Test dataset paths"""
    dataset_paths = {
        'Sim2Real_30k': 'K:/Sim2Real_30k/images',
        'DenseSIRST': 'F:/dataset/Infrared/DenseSIRST/DenseSIRST/SIRSTdevkit/PNGImages',
        'IRSatVideo-LEO': 'F:/dataset/Infrared/IRSatVideo-LEO/images',
        'IRSTD-1k': 'F:/dataset/Infrared/IRSTD-1k/IRSTD-1k/images',
        'NoisySIRST': 'F:/dataset/Infrared/NoisySIRST/sirst_random_noise/sirst_random_noise_30/train_rand30/trainval_rand_30/images',
        'NUAA-SIRST': 'F:/dataset/Infrared/NUAA-SIRST/images',
        'NUDT-SIRST': 'F:/dataset/Infrared/NUDT_SIRST/NUDT/trainval/images',
        'WideIRSTD': 'F:/dataset/Infrared/WideIRSTD-Full Dataset/train/images'
    }
    
    available_datasets = []
    
    print("\nTesting dataset paths...")
    for name, path in dataset_paths.items():
        if Path(path).exists():
            print(f"✓ {name}: {path}")
            available_datasets.append(name)
        else:
            print(f"❌ {name}: {path} (not found)")
    
    print(f"\nAvailable datasets: {len(available_datasets)}")
    return available_datasets

def test_analyzer_creation():
    """Test analyzer creation"""
    try:
        print("\nTesting analyzer creation...")
        from cross_dataset_domain_analyzer import CrossDatasetDomainAnalyzer
        
        analyzer = CrossDatasetDomainAnalyzer("test_output", max_images_per_dataset=5)
        print("✓ Analyzer created successfully")
        return analyzer
        
    except Exception as e:
        print(f"❌ Analyzer creation failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    print("🧪 Testing Cross-Dataset Domain Analyzer")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        return 1
    
    # Test dataset paths
    available_datasets = test_dataset_paths()
    
    if len(available_datasets) < 2:
        print(f"\n⚠️ Need at least 2 datasets for analysis. Found: {len(available_datasets)}")
        print("Please ensure dataset paths are correct.")
        return 1
    
    # Test analyzer creation
    analyzer = test_analyzer_creation()
    if analyzer is None:
        return 1
    
    print(f"\n✅ All tests passed!")
    print(f"📊 Ready to analyze {len(available_datasets)} datasets")
    print(f"🚀 You can now run the full analysis")
    
    return 0

if __name__ == "__main__":
    exit(main())
