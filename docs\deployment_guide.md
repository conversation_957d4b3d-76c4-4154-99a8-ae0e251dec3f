# Sim2Real_30k 数据集工具部署指南

## 概述

本指南帮助您在新的计算机上部署和配置 Sim2Real_30k 红外小目标检测数据集的处理工具。

## 📁 项目结构

```
Sim2Real/
├── config/
│   ├── processing_config.json     # 数据处理配置
│   └── paths_config.json          # 路径配置（新增）
├── scripts/
│   ├── dataset_scanner.py         # 数据集扫描工具
│   ├── data_processor.py          # 数据处理管道
│   ├── dataset_splitter.py        # 数据集划分工具
│   ├── dataset_visualizer.py      # 可视化工具（中文）
│   ├── dataset_visualizer_en.py   # 可视化工具（英文）
│   ├── domain_gap_analyzer.py     # 域差距分析工具
│   └── dataset_merger.py          # 数据集合并工具
├── docs/
│   ├── dataset_filtering_strategy.md
│   ├── scanner_script_guide.md
│   ├── data_processing_guide.md
│   ├── dataset_split_guide.md
│   ├── visualization_usage_guide.md
│   └── deployment_guide.md        # 本文档
├── requirements.txt               # Python依赖
└── README.md                      # 项目说明
```

## 🚀 快速部署步骤

### 1. 环境准备

#### Python环境
```bash
# 推荐使用Python 3.8+
python --version

# 创建虚拟环境（推荐）
python -m venv sim2real_env
source sim2real_env/bin/activate  # Linux/Mac
# 或
sim2real_env\Scripts\activate     # Windows
```

#### 安装依赖
```bash
# 基础依赖
pip install numpy matplotlib seaborn Pillow tqdm pathlib2

# 深度学习依赖（用于域差距分析）
pip install torch torchvision scikit-learn

# 或者一次性安装
pip install -r requirements.txt
```

### 2. 配置路径

#### 创建路径配置文件
创建 `config/paths_config.json`：

```json
{
  "dataset_root": "/path/to/your/dataset",
  "output_root": "/path/to/output",
  "temp_dir": "/path/to/temp",
  "examples": {
    "windows": {
      "dataset_root": "F:\\Sim2Real_30k",
      "output_root": "F:\\Sim2Real_30k_output",
      "temp_dir": "F:\\temp"
    },
    "linux": {
      "dataset_root": "/mnt/data/Sim2Real_30k",
      "output_root": "/mnt/output/Sim2Real_30k",
      "temp_dir": "/tmp/sim2real"
    },
    "mac": {
      "dataset_root": "/Volumes/Data/Sim2Real_30k",
      "output_root": "/Users/<USER>/output/Sim2Real_30k",
      "temp_dir": "/tmp/sim2real"
    }
  }
}
```

#### 更新处理配置
修改 `config/processing_config.json`，使用相对路径或配置变量：

```json
{
  "target_count": 30000,
  "frame_interval": [5, 10],
  "random_offset": 2,
  "size_distribution": {
    "0-4px": 0.25,
    "5-9px": 0.35,
    "10-14px": 0.20,
    "15-19px": 0.10,
    "20-24px": 0.06,
    "25-29px": 0.03,
    "30-34px": 0.01
  },
  "quality_threshold": 0.8,
  "max_errors_per_batch": 10,
  "image_size": {
    "width": 1920,
    "height": 1080
  },
  "output_format": {
    "image_format": "png",
    "annotation_format": "coco",
    "naming_pattern": "batch{batch_id}_{image_id:06d}.png"
  },
  "processing_options": {
    "copy_images": true,
    "generate_reports": true,
    "validate_outputs": true,
    "create_visualization": false
  },
  "paths": {
    "use_config_file": true,
    "config_file": "config/paths_config.json"
  }
}
```

### 3. 数据集结构

确保您的数据集遵循以下结构：

```
数据集根目录/
├── 原始数据/
│   ├── batch1/
│   │   ├── *.png
│   │   └── *.json
│   ├── batch2/
│   └── ...
└── 处理后数据/
    ├── images/
    ├── annotations/
    ├── reports/
    └── visualization/
```

## 🔧 配置工具

### 路径配置助手

创建 `scripts/setup_paths.py`：

```python
#!/usr/bin/env python3
"""
路径配置助手
帮助用户快速配置数据集路径
"""

import json
import os
from pathlib import Path

def setup_paths():
    """交互式路径配置"""
    print("=== Sim2Real_30k 路径配置助手 ===")
    
    # 获取用户输入
    dataset_root = input("请输入数据集根目录路径: ").strip().strip('"\'')
    output_root = input("请输入输出目录路径 (可选，默认为数据集目录下的output): ").strip().strip('"\'')
    temp_dir = input("请输入临时目录路径 (可选，默认为系统临时目录): ").strip().strip('"\'')
    
    # 设置默认值
    if not output_root:
        output_root = str(Path(dataset_root) / "output")
    if not temp_dir:
        temp_dir = str(Path.home() / "temp" / "sim2real")
    
    # 验证路径
    dataset_path = Path(dataset_root)
    if not dataset_path.exists():
        print(f"警告: 数据集路径不存在: {dataset_root}")
        create = input("是否创建该目录? (y/n): ").lower() == 'y'
        if create:
            dataset_path.mkdir(parents=True, exist_ok=True)
    
    # 创建配置
    config = {
        "dataset_root": dataset_root,
        "output_root": output_root,
        "temp_dir": temp_dir,
        "created_by": "setup_paths.py",
        "platform": os.name
    }
    
    # 保存配置
    config_dir = Path("config")
    config_dir.mkdir(exist_ok=True)
    
    config_file = config_dir / "paths_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print(f"\n✓ 配置已保存到: {config_file}")
    print(f"数据集路径: {dataset_root}")
    print(f"输出路径: {output_root}")
    print(f"临时路径: {temp_dir}")

if __name__ == "__main__":
    setup_paths()
```

## 📋 使用流程

### 1. 初始化配置
```bash
# 运行路径配置助手
python scripts/setup_paths.py

# 或手动编辑配置文件
nano config/paths_config.json
```

### 2. 数据扫描
```bash
# 使用配置文件中的路径
python scripts/dataset_scanner.py -c config/paths_config.json -o scan_results.json

# 或直接指定路径
python scripts/dataset_scanner.py -p "/your/dataset/path" -o scan_results.json
```

### 3. 数据处理
```bash
# 使用配置文件
python scripts/data_processor.py scan_results.json -c config/processing_config.json

# 或指定输出路径
python scripts/data_processor.py scan_results.json -o "/your/output/path" -n 30000
```

### 4. 数据集划分
```bash
# 划分训练集和验证集
python scripts/dataset_splitter.py "/your/output/path/annotations/instances_complete.json" -o "/your/output/path" --train-ratio 0.7
```

### 5. 可视化分析
```bash
# 基础可视化（推荐英文版）
python scripts/dataset_visualizer_en.py "/your/output/path/annotations/instances_complete.json" -o "/your/output/path/visualization"

# 域差距分析
python scripts/domain_gap_analyzer.py "/your/output/path/annotations/instances_complete.json" "/your/output/path/images" -o "/your/output/path/domain_analysis"
```

## 🔄 跨平台兼容性

### Windows
```bash
# 路径示例
"F:\\Sim2Real_30k"
"F:\\Sim2Real_30k\\output"

# 运行示例
python scripts\dataset_scanner.py -p "F:\Sim2Real_30k" -o scan_results.json
```

### Linux/Unix
```bash
# 路径示例
"/mnt/data/Sim2Real_30k"
"/home/<USER>/output/Sim2Real_30k"

# 运行示例
python scripts/dataset_scanner.py -p "/mnt/data/Sim2Real_30k" -o scan_results.json
```

### macOS
```bash
# 路径示例
"/Volumes/Data/Sim2Real_30k"
"/Users/<USER>/Documents/Sim2Real_30k"

# 运行示例
python scripts/dataset_scanner.py -p "/Volumes/Data/Sim2Real_30k" -o scan_results.json
```

## 🚨 常见问题

### 路径问题
- **空格路径**: 使用引号包围路径
- **中文路径**: 确保系统支持UTF-8编码
- **权限问题**: 确保对目标目录有读写权限

### 依赖问题
```bash
# 如果pip安装失败
pip install --upgrade pip
pip install --user package_name

# 如果torch安装失败
pip install torch torchvision --index-url https://download.pytorch.org/whl/cpu
```

### 内存问题
- 减少处理的图像数量
- 使用较小的batch size
- 关闭不必要的程序

## 📦 打包和分发

### 创建便携版本
```bash
# 创建完整的项目包
tar -czf sim2real_tools.tar.gz Sim2Real/

# 或使用zip
zip -r sim2real_tools.zip Sim2Real/
```

### 环境导出
```bash
# 导出Python环境
pip freeze > requirements.txt

# 导出conda环境
conda env export > environment.yml
```

## 🔧 自定义配置

### 修改处理参数
编辑 `config/processing_config.json`：
- `target_count`: 目标图像数量
- `size_distribution`: 尺寸分布比例
- `frame_interval`: 时序采样间隔

### 修改可视化参数
在脚本中修改：
- 图像尺寸和DPI
- 颜色方案
- 统计分析方法

## 📞 技术支持

如果遇到问题：
1. 检查路径配置是否正确
2. 确认依赖是否完整安装
3. 查看错误日志和输出信息
4. 参考相关文档和示例

---

**注意**: 首次部署时建议先在小数据集上测试，确认所有工具正常工作后再处理完整数据集。
