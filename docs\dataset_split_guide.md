# 数据集划分工具使用指南

## 概述
数据集划分工具用于将处理好的COCO格式数据集按batch划分为训练集和验证集，确保同一batch的数据不会同时出现在训练集和验证集中，避免数据泄露。

## 功能特性
- ✅ 按batch进行划分，避免数据泄露
- ✅ 可自定义训练集/验证集比例
- ✅ 只划分标注文件，不复制图像
- ✅ 生成详细的划分报告
- ✅ 支持随机种子，确保结果可重现

## 使用方法

### 1. 基本用法
```bash
# 使用默认7:3比例划分
python scripts/dataset_splitter.py "F:\Sim2Real_30k\annotations\instances_train.json" -o "F:\Sim2Real_30k"

# 自定义比例（8:2）
python scripts/dataset_splitter.py "F:\Sim2Real_30k\annotations\instances_train.json" -o "F:\Sim2Real_30k" --train-ratio 0.8

# 使用固定随机种子
python scripts/dataset_splitter.py "F:\Sim2Real_30k\annotations\instances_train.json" -o "F:\Sim2Real_30k" --seed 42
```

### 2. 参数说明
- `annotation_file`: COCO格式标注文件路径
- `-o, --output`: 输出目录
- `--train-ratio`: 训练集比例（默认0.7）
- `--seed`: 随机种子（可选）

## 输出结构

### 划分前
```
F:\Sim2Real_30k\
├── images\
│   ├── batch1_000001.png
│   ├── batch2_000001.png
│   └── ...
└── annotations\
    └── instances_train.json    # 原始完整标注文件（30,000张图像）
```

### 划分后
```
F:\Sim2Real_30k\
├── images\                          # 图像文件保持不变
│   ├── batch1_000001.png
│   ├── batch2_000001.png
│   └── ...
├── annotations\
│   ├── instances_train.json         # 原始完整文件（保留，30,000张）
│   ├── instances_train_split.json   # 训练集标注（~21,000张）
│   └── instances_val_split.json     # 验证集标注（~9,000张）
└── reports\
    └── dataset_split_report.json    # 划分报告
```

## 划分策略

### 1. Batch级别划分
- 以batch为单位进行划分
- 确保同一batch的所有图像都在同一个集合中
- 避免时序相关的数据泄露

### 2. 随机分配
- 随机打乱batch顺序
- 按比例分配到训练集和验证集
- 支持设置随机种子确保可重现

### 3. 比例控制
- 默认7:3（训练:验证）
- 可自定义比例（建议0.1-0.9之间）
- 实际比例可能因batch数量略有偏差

## 输出文件说明

### 1. instances_train_split.json
训练集标注文件，包含：
- 训练集batch的所有图像信息
- 对应的标注信息
- 完整的COCO格式结构

### 2. instances_val_split.json
验证集标注文件，包含：
- 验证集batch的所有图像信息
- 对应的标注信息
- 完整的COCO格式结构

### 3. instances_train.json（原始文件）
完整数据集标注文件，包含：
- 所有30,000张图像的信息
- 所有76,582个标注
- 保持原始完整性，便于重新划分

### 3. dataset_split_report.json
划分报告，包含：
```json
{
  "split_config": {
    "train_ratio": 0.7,
    "val_ratio": 0.3,
    "split_method": "by_batch"
  },
  "summary": {
    "total_batches": 50,
    "train_batches": 35,
    "val_batches": 15,
    "total_images": 30000,
    "train_images": 21000,
    "val_images": 9000
  },
  "train_batches": {
    "batch1": 600,
    "batch3": 580,
    ...
  },
  "val_batches": {
    "batch2": 620,
    "batch5": 590,
    ...
  },
  "batch_list": {
    "train": ["batch1", "batch3", ...],
    "val": ["batch2", "batch5", ...]
  }
}
```

## 使用示例

### 示例1: 标准7:3划分
```bash
python scripts/dataset_splitter.py "F:\Sim2Real_30k\annotations\instances_train.json" -o "F:\Sim2Real_30k"
```

### 示例2: 8:2划分用于大数据集
```bash
python scripts/dataset_splitter.py "F:\Sim2Real_30k\annotations\instances_train.json" -o "F:\Sim2Real_30k" --train-ratio 0.8
```

### 示例3: 可重现的划分
```bash
python scripts/dataset_splitter.py "F:\Sim2Real_30k\annotations\instances_train.json" -o "F:\Sim2Real_30k" --seed 42
```

## 验证划分结果

### 1. 检查文件
```bash
# 检查生成的文件
ls "F:\Sim2Real_30k\annotations\"
ls "F:\Sim2Real_30k\reports\"
```

### 2. 验证数据完整性
```python
import json

# 加载原始和划分后的文件
with open("F:/Sim2Real_30k/annotations/instances_train.json") as f:
    original = json.load(f)

with open("F:/Sim2Real_30k/annotations/instances_train.json") as f:
    train = json.load(f)

with open("F:/Sim2Real_30k/annotations/instances_val.json") as f:
    val = json.load(f)

# 验证数量
print(f"原始: {len(original['images'])} 图像, {len(original['annotations'])} 标注")
print(f"训练: {len(train['images'])} 图像, {len(train['annotations'])} 标注")
print(f"验证: {len(val['images'])} 图像, {len(val['annotations'])} 标注")
print(f"总和: {len(train['images']) + len(val['images'])} 图像")
```

## 注意事项

1. **备份原始文件**: 划分前建议备份原始标注文件
2. **磁盘空间**: 标注文件相对较小，通常不会占用太多空间
3. **batch命名**: 确保图像文件名遵循 `batch{N}_{id}.png` 格式
4. **随机性**: 不设置seed时每次运行结果会不同
5. **比例精度**: 实际比例可能因batch数量而略有偏差

## 下一步

划分完成后，你可以：
1. 使用训练集进行模型训练
2. 使用验证集进行模型评估
3. 根据需要调整划分比例重新划分
4. 进行数据增强或其他预处理
