# Sim2Real_30k 数据集网页云服务器部署指南
## 服务器IP: **************

## 🚀 通过网页端部署方案

由于VSCode远程连接问题，我们使用服务商网页端进行部署。

## 📋 部署前准备

### 1. 准备部署文件
首先在本地打包网页文件：

```bash
# 在本地Windows系统中
cd K:\Sim2Real_30k\final_with_batch_analysis
# 创建压缩包（使用WinRAR或7-Zip）
# 将所有文件压缩为 sim2real_website.zip
```

**包含文件**:
- `sim2real_30k_improved.html` (主页面)
- `batch_pca_visualization.png`
- `batch_tsne_visualization.png`
- `internal_domain_similarity.png`
- `batch_feature_distribution.png`
- `batch_domain_evolution.png`
- `size_example_*.png` (4个文件)
- `background_examples.png`
- `clustering_analysis.png`
- `tsne_visualization.png`
- 其他所有 `.png` 文件

## 🌐 方案一：通过服务商网页控制台部署

### 步骤1：登录服务商控制台
1. 打开浏览器，访问您的云服务商控制台
2. 登录账户，找到服务器 `**************`
3. 进入服务器管理界面

### 步骤2：使用网页终端
大多数云服务商都提供网页版终端：
1. 在服务器管理页面找到"远程连接"或"网页终端"
2. 点击进入网页版SSH终端
3. 输入用户名和密码登录

### 步骤3：安装Web服务器
在网页终端中执行：

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Nginx
sudo apt install nginx -y

# 启动Nginx服务
sudo systemctl start nginx
sudo systemctl enable nginx

# 检查状态
sudo systemctl status nginx

# 检查防火墙
sudo ufw allow 80
sudo ufw allow 443
sudo ufw status
```

### 步骤4：上传网页文件

#### 方法A：使用服务商文件管理器
1. 在控制台找到"文件管理"功能
2. 导航到 `/var/www/html/` 目录
3. 删除默认文件：`sudo rm -rf /var/www/html/*`
4. 上传 `sim2real_website.zip`
5. 解压文件：`sudo unzip sim2real_website.zip -d /var/www/html/`

#### 方法B：使用wget下载（推荐）
如果您有文件托管服务：

```bash
# 清空默认目录
sudo rm -rf /var/www/html/*

# 下载文件（需要先上传到文件托管服务）
cd /var/www/html/
sudo wget [您的文件下载链接] -O sim2real_website.zip

# 解压
sudo unzip sim2real_website.zip

# 设置权限
sudo chown -R www-data:www-data /var/www/html/
sudo chmod -R 755 /var/www/html/
```

### 步骤5：配置Nginx
编辑Nginx配置：

```bash
sudo nano /etc/nginx/sites-available/default
```

配置内容：
```nginx
server {
    listen 80 default_server;
    listen [::]:80 default_server;

    root /var/www/html;
    index sim2real_30k_improved.html index.html;

    server_name **************;

    location / {
        try_files $uri $uri/ =404;
    }

    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 设置图片缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

重启Nginx：
```bash
sudo nginx -t
sudo systemctl restart nginx
```

## 🌐 方案二：使用临时文件传输服务

### 步骤1：上传到临时文件服务
1. 使用 WeTransfer、百度网盘、或其他文件传输服务
2. 上传 `sim2real_website.zip`
3. 获取下载链接

### 步骤2：在服务器下载
```bash
# 在网页终端中
cd /var/www/html/
sudo rm -rf *

# 下载文件（替换为实际下载链接）
sudo wget "下载链接" -O sim2real_website.zip

# 解压
sudo unzip sim2real_website.zip

# 设置权限
sudo chown -R www-data:www-data /var/www/html/
sudo chmod -R 755 /var/www/html/
```

## 🌐 方案三：使用GitHub Pages作为中转

### 步骤1：上传到GitHub
1. 创建GitHub仓库 `sim2real-30k-dataset`
2. 上传所有网页文件
3. 重命名 `sim2real_30k_improved.html` 为 `index.html`

### 步骤2：从GitHub下载到服务器
```bash
# 在服务器上
cd /var/www/html/
sudo rm -rf *

# 克隆仓库
sudo git clone https://github.com/yourusername/sim2real-30k-dataset.git .

# 设置权限
sudo chown -R www-data:www-data /var/www/html/
sudo chmod -R 755 /var/www/html/
```

## 🔧 部署后配置

### 1. 测试访问
在浏览器中访问：
```
http://**************/sim2real_30k_improved.html
```

### 2. 设置默认页面
```bash
# 创建软链接，使主页成为默认页面
sudo ln -sf /var/www/html/sim2real_30k_improved.html /var/www/html/index.html
```

### 3. 配置SSL证书（可选）
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx -y

# 如果有域名，可以申请SSL证书
sudo certbot --nginx -d yourdomain.com
```

## 🛠️ 故障排除

### 1. 检查Nginx状态
```bash
sudo systemctl status nginx
sudo nginx -t
```

### 2. 检查文件权限
```bash
ls -la /var/www/html/
sudo chown -R www-data:www-data /var/www/html/
sudo chmod -R 755 /var/www/html/
```

### 3. 查看错误日志
```bash
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log
```

### 4. 检查防火墙
```bash
sudo ufw status
sudo ufw allow 80
sudo ufw allow 443
```

## 📱 移动端优化

网页已经包含响应式设计，在移动设备上也能良好显示。

## 🔄 更新网页

当需要更新网页时：
```bash
# 备份当前版本
sudo cp -r /var/www/html /var/www/html_backup_$(date +%Y%m%d)

# 上传新版本并解压
cd /var/www/html/
sudo rm -rf *
sudo wget "新版本下载链接" -O sim2real_website_new.zip
sudo unzip sim2real_website_new.zip
sudo chown -R www-data:www-data /var/www/html/
sudo chmod -R 755 /var/www/html/
```

## 📊 访问统计（可选）

### 安装简单的访问统计
```bash
# 安装GoAccess
sudo apt install goaccess -y

# 生成访问报告
sudo goaccess /var/log/nginx/access.log -o /var/www/html/stats.html --log-format=COMBINED
```

## 🎯 推荐部署流程

1. **准备文件**: 在本地打包所有网页文件
2. **上传方式**: 推荐使用GitHub或百度网盘作为中转
3. **服务器操作**: 通过网页终端执行命令
4. **测试访问**: 确保 `http://**************` 可以正常访问
5. **优化配置**: 设置缓存和压缩

## 📞 技术支持

如果遇到问题：
1. 检查服务商控制台的操作日志
2. 查看Nginx错误日志
3. 确认防火墙设置
4. 验证文件权限

---

**部署完成后访问地址**: `http://**************/sim2real_30k_improved.html`

**预计部署时间**: 15-30分钟

**推荐方案**: 方案二（临时文件传输服务）最简单快捷
