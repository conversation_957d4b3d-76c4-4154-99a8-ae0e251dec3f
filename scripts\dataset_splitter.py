#!/usr/bin/env python3
"""
数据集划分工具
按batch划分训练集和测试集，避免数据泄露
"""

import json
import random
import argparse
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Tuple

class DatasetSplitter:
    def __init__(self, train_ratio: float = 0.7):
        self.train_ratio = train_ratio
        self.val_ratio = 1.0 - train_ratio
        
    def load_coco_annotation(self, annotation_file: str) -> Dict:
        """加载COCO格式标注文件"""
        print(f"加载COCO标注文件: {annotation_file}")
        
        with open(annotation_file, 'r', encoding='utf-8') as f:
            coco_data = json.load(f)
        
        print(f"✓ 标注文件加载完成:")
        print(f"  - 图像数: {len(coco_data['images'])}")
        print(f"  - 标注数: {len(coco_data['annotations'])}")
        print(f"  - 类别数: {len(coco_data['categories'])}")
        
        return coco_data
    
    def analyze_batch_distribution(self, coco_data: Dict) -> Dict[str, List[int]]:
        """分析batch分布"""
        print("分析batch分布...")
        
        batch_images = defaultdict(list)
        
        for image in coco_data['images']:
            # 从文件名中提取batch信息
            filename = image['file_name']
            # 假设文件名格式为: batch{N}_{id}.png
            if filename.startswith('batch'):
                batch_id = filename.split('_')[0]  # 提取batch部分
                batch_images[batch_id].append(image['id'])
        
        print(f"✓ 发现 {len(batch_images)} 个batch:")
        for batch_id, image_ids in sorted(batch_images.items()):
            print(f"  - {batch_id}: {len(image_ids)} 张图像")
        
        return batch_images
    
    def split_batches(self, batch_images: Dict[str, List[int]]) -> Tuple[List[str], List[str]]:
        """按batch划分训练集和验证集"""
        print(f"按batch划分数据集 (训练集:{self.train_ratio:.1%}, 验证集:{self.val_ratio:.1%})...")
        
        # 获取所有batch ID并随机打乱
        batch_ids = list(batch_images.keys())
        random.shuffle(batch_ids)
        
        # 计算训练集batch数量
        total_batches = len(batch_ids)
        train_batch_count = int(total_batches * self.train_ratio)
        
        # 划分batch
        train_batches = batch_ids[:train_batch_count]
        val_batches = batch_ids[train_batch_count:]
        
        # 统计图像数量
        train_image_count = sum(len(batch_images[batch_id]) for batch_id in train_batches)
        val_image_count = sum(len(batch_images[batch_id]) for batch_id in val_batches)
        total_images = train_image_count + val_image_count
        
        print(f"✓ Batch划分完成:")
        print(f"  - 训练集: {len(train_batches)} 个batch, {train_image_count} 张图像 ({train_image_count/total_images:.1%})")
        print(f"  - 验证集: {len(val_batches)} 个batch, {val_image_count} 张图像 ({val_image_count/total_images:.1%})")
        
        print(f"\n训练集batch: {sorted(train_batches)}")
        print(f"验证集batch: {sorted(val_batches)}")
        
        return train_batches, val_batches
    
    def create_split_annotations(self, coco_data: Dict, batch_images: Dict[str, List[int]], 
                                train_batches: List[str], val_batches: List[str]) -> Tuple[Dict, Dict]:
        """创建训练集和验证集的标注文件"""
        print("创建训练集和验证集标注文件...")
        
        # 获取训练集和验证集的图像ID
        train_image_ids = set()
        val_image_ids = set()
        
        for batch_id in train_batches:
            train_image_ids.update(batch_images[batch_id])
        
        for batch_id in val_batches:
            val_image_ids.update(batch_images[batch_id])
        
        # 创建训练集标注
        train_coco = {
            "info": coco_data["info"].copy(),
            "licenses": coco_data["licenses"],
            "categories": coco_data["categories"],
            "images": [],
            "annotations": []
        }
        train_coco["info"]["description"] += " - 训练集"
        
        # 创建验证集标注
        val_coco = {
            "info": coco_data["info"].copy(),
            "licenses": coco_data["licenses"],
            "categories": coco_data["categories"],
            "images": [],
            "annotations": []
        }
        val_coco["info"]["description"] += " - 验证集"
        
        # 分配图像
        for image in coco_data['images']:
            if image['id'] in train_image_ids:
                train_coco['images'].append(image)
            elif image['id'] in val_image_ids:
                val_coco['images'].append(image)
        
        # 分配标注
        for annotation in coco_data['annotations']:
            if annotation['image_id'] in train_image_ids:
                train_coco['annotations'].append(annotation)
            elif annotation['image_id'] in val_image_ids:
                val_coco['annotations'].append(annotation)
        
        print(f"✓ 标注文件创建完成:")
        print(f"  - 训练集: {len(train_coco['images'])} 张图像, {len(train_coco['annotations'])} 个标注")
        print(f"  - 验证集: {len(val_coco['images'])} 张图像, {len(val_coco['annotations'])} 个标注")
        
        return train_coco, val_coco
    
    def save_split_files(self, train_coco: Dict, val_coco: Dict, output_dir: str):
        """保存划分后的标注文件"""
        output_path = Path(output_dir)
        annotations_dir = output_path / "annotations"
        annotations_dir.mkdir(parents=True, exist_ok=True)

        # 保存训练集标注 - 使用新的命名避免覆盖原文件
        train_file = annotations_dir / "instances_train_split.json"
        with open(train_file, 'w', encoding='utf-8') as f:
            json.dump(train_coco, f, ensure_ascii=False, indent=2)
        print(f"✓ 训练集标注已保存: {train_file}")

        # 保存验证集标注
        val_file = annotations_dir / "instances_val_split.json"
        with open(val_file, 'w', encoding='utf-8') as f:
            json.dump(val_coco, f, ensure_ascii=False, indent=2)
        print(f"✓ 验证集标注已保存: {val_file}")

        return train_file, val_file
    
    def generate_split_report(self, batch_images: Dict[str, List[int]], 
                             train_batches: List[str], val_batches: List[str], 
                             output_dir: str):
        """生成划分报告"""
        output_path = Path(output_dir)
        reports_dir = output_path / "reports"
        reports_dir.mkdir(parents=True, exist_ok=True)
        
        # 统计信息
        train_stats = {}
        val_stats = {}
        
        for batch_id in train_batches:
            train_stats[batch_id] = len(batch_images[batch_id])
        
        for batch_id in val_batches:
            val_stats[batch_id] = len(batch_images[batch_id])
        
        split_report = {
            "split_config": {
                "train_ratio": self.train_ratio,
                "val_ratio": self.val_ratio,
                "split_method": "by_batch"
            },
            "summary": {
                "total_batches": len(batch_images),
                "train_batches": len(train_batches),
                "val_batches": len(val_batches),
                "total_images": sum(len(images) for images in batch_images.values()),
                "train_images": sum(train_stats.values()),
                "val_images": sum(val_stats.values())
            },
            "train_batches": train_stats,
            "val_batches": val_stats,
            "batch_list": {
                "train": sorted(train_batches),
                "val": sorted(val_batches)
            }
        }
        
        report_file = reports_dir / "dataset_split_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(split_report, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 划分报告已保存: {report_file}")
        return report_file
    
    def split_dataset(self, annotation_file: str, output_dir: str, seed: int = None):
        """主要的数据集划分流程"""
        print("="*80)
        print("红外小目标检测数据集划分工具")
        print("="*80)
        
        # 设置随机种子
        if seed is not None:
            random.seed(seed)
            print(f"使用随机种子: {seed}")
        
        # 1. 加载COCO标注
        coco_data = self.load_coco_annotation(annotation_file)
        
        # 2. 分析batch分布
        batch_images = self.analyze_batch_distribution(coco_data)
        
        # 3. 划分batch
        train_batches, val_batches = self.split_batches(batch_images)
        
        # 4. 创建划分后的标注文件
        train_coco, val_coco = self.create_split_annotations(
            coco_data, batch_images, train_batches, val_batches
        )
        
        # 5. 保存文件
        train_file, val_file = self.save_split_files(train_coco, val_coco, output_dir)
        
        # 6. 生成报告
        report_file = self.generate_split_report(
            batch_images, train_batches, val_batches, output_dir
        )
        
        print(f"\n{'='*80}")
        print("数据集划分完成！")
        print(f"{'='*80}")
        print(f"输出目录: {output_dir}")
        print(f"训练集标注: {train_file}")
        print(f"验证集标注: {val_file}")
        print(f"划分报告: {report_file}")
        print(f"{'='*80}")

def main():
    parser = argparse.ArgumentParser(description="红外小目标检测数据集划分工具")
    parser.add_argument('annotation_file', help='COCO格式标注文件路径')
    parser.add_argument('-o', '--output', required=True, help='输出目录')
    parser.add_argument('--train-ratio', type=float, default=0.7, 
                       help='训练集比例 (默认: 0.7)')
    parser.add_argument('--seed', type=int, help='随机种子')
    
    args = parser.parse_args()
    
    # 验证输入文件
    if not Path(args.annotation_file).exists():
        print(f"❌ 标注文件不存在: {args.annotation_file}")
        return 1
    
    # 验证比例
    if not 0.1 <= args.train_ratio <= 0.9:
        print(f"❌ 训练集比例应在0.1-0.9之间: {args.train_ratio}")
        return 1
    
    # 创建划分器并执行
    splitter = DatasetSplitter(train_ratio=args.train_ratio)
    splitter.split_dataset(args.annotation_file, args.output, args.seed)
    
    return 0

if __name__ == "__main__":
    exit(main())
