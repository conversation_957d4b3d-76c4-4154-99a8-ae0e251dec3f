#!/usr/bin/env python3
"""
Comprehensive Dataset Website Generator
Create a professional Chinese dataset introduction website
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import argparse
import base64
from datetime import datetime
from typing import Dict, List, Optional
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

# Set style
plt.rcParams['font.family'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

class ComprehensiveDatasetWebsite:
    def __init__(self, dataset_root: str, output_dir: str):
        self.dataset_root = Path(dataset_root)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Load all analysis data
        self.dataset_info = self.load_dataset_info()
        self.visualization_files = self.find_all_visualizations()
        self.analysis_reports = self.load_analysis_reports()
        
    def load_dataset_info(self) -> Dict:
        """Load basic dataset information"""
        annotation_file = self.dataset_root / "annotations" / "instances_complete.json"
        if annotation_file.exists():
            with open(annotation_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return {
                'total_images': len(data['images']),
                'total_annotations': len(data['annotations']),
                'categories': len(data['categories']),
                'images': data['images'],
                'annotations': data['annotations']
            }
        return {}
    
    def find_all_visualizations(self) -> Dict[str, Path]:
        """Find all visualization files"""
        files = {}
        
        # Search in multiple directories
        search_dirs = [
            'visualization_en',
            'enhanced_visualization', 
            'domain_analysis',
            'cross_dataset_analysis'
        ]
        
        for dir_name in search_dirs:
            viz_dir = self.dataset_root / dir_name
            if viz_dir.exists():
                for file in viz_dir.glob("*.png"):
                    key = f"{dir_name}_{file.stem}"
                    files[key] = file
        
        return files
    
    def load_analysis_reports(self) -> Dict:
        """Load all analysis reports"""
        reports = {}
        
        # Load enhanced dataset report
        enhanced_report = self.dataset_root / "enhanced_visualization" / "enhanced_dataset_report.json"
        if enhanced_report.exists():
            with open(enhanced_report, 'r', encoding='utf-8') as f:
                reports['enhanced'] = json.load(f)
        
        # Load domain analysis report
        domain_report = self.dataset_root / "domain_analysis" / "domain_gap_report.json"
        if domain_report.exists():
            with open(domain_report, 'r', encoding='utf-8') as f:
                reports['domain'] = json.load(f)
        
        # Load cross-dataset report
        cross_report = self.dataset_root / "cross_dataset_analysis" / "cross_dataset_analysis_report.json"
        if cross_report.exists():
            with open(cross_report, 'r', encoding='utf-8') as f:
                reports['cross_dataset'] = json.load(f)
        
        return reports
    
    def encode_image_to_base64(self, image_path: Path) -> str:
        """Convert image to base64 for HTML embedding"""
        with open(image_path, 'rb') as f:
            image_data = f.read()
        return base64.b64encode(image_data).decode('utf-8')
    
    def create_optimized_visualizations(self):
        """Create optimized visualizations with Chinese explanations"""
        print("🎨 创建优化的可视化图表...")
        
        # Create individual size distribution plots
        self.create_size_distribution_plots()
        
        # Create batch analysis plots
        self.create_batch_analysis_plots()
        
        # Create spatial distribution plot
        self.create_spatial_distribution_plot()
        
        print("✅ 优化可视化图表创建完成")
    
    def create_size_distribution_plots(self):
        """Create individual size distribution plots"""
        if not self.dataset_info:
            return
        
        # Extract target sizes
        target_sizes = []
        for ann in self.dataset_info['annotations']:
            bbox = ann['bbox']
            size = max(bbox[2], bbox[3])  # max of width, height
            target_sizes.append(size)
        
        if not target_sizes:
            return
        
        # 1. Size histogram
        plt.figure(figsize=(10, 6))
        plt.hist(target_sizes, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        plt.title('目标尺寸分布直方图', fontsize=16, fontweight='bold')
        plt.xlabel('目标尺寸 (像素)', fontsize=12)
        plt.ylabel('频次', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # Add statistics text
        mean_size = np.mean(target_sizes)
        std_size = np.std(target_sizes)
        plt.text(0.7, 0.8, f'平均尺寸: {mean_size:.1f}px\n标准差: {std_size:.1f}px', 
                transform=plt.gca().transAxes, fontsize=11, 
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
        
        plt.tight_layout()
        plt.savefig(self.output_dir / "size_histogram_cn.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. Size categories pie chart
        size_categories = {
            '微小目标 (≤8px)': sum(1 for s in target_sizes if s <= 8),
            '小目标 (8-16px)': sum(1 for s in target_sizes if 8 < s <= 16),
            '中等目标 (16-24px)': sum(1 for s in target_sizes if 16 < s <= 24),
            '大目标 (>24px)': sum(1 for s in target_sizes if s > 24)
        }
        
        plt.figure(figsize=(8, 8))
        colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99']
        wedges, texts, autotexts = plt.pie(size_categories.values(), 
                                          labels=size_categories.keys(), 
                                          autopct='%1.1f%%', 
                                          startangle=90,
                                          colors=colors)
        
        plt.title('目标尺寸类别分布', fontsize=16, fontweight='bold')
        
        # Beautify the pie chart
        for autotext in autotexts:
            autotext.set_color('white')
            autotext.set_fontweight('bold')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / "size_categories_cn.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        # 3. Cumulative distribution
        plt.figure(figsize=(10, 6))
        sorted_sizes = np.sort(target_sizes)
        y = np.arange(1, len(sorted_sizes) + 1) / len(sorted_sizes)
        plt.plot(sorted_sizes, y, linewidth=3, color='red', label='累积分布')
        
        # Add percentile lines
        percentiles = [25, 50, 75, 90]
        for p in percentiles:
            value = np.percentile(target_sizes, p)
            plt.axvline(value, color='gray', linestyle='--', alpha=0.7)
            plt.text(value, 0.1 + p/100 * 0.1, f'{p}%: {value:.1f}px', 
                    rotation=90, fontsize=10)
        
        plt.title('目标尺寸累积分布函数', fontsize=16, fontweight='bold')
        plt.xlabel('目标尺寸 (像素)', fontsize=12)
        plt.ylabel('累积概率', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.tight_layout()
        plt.savefig(self.output_dir / "size_cdf_cn.png", dpi=300, bbox_inches='tight')
        plt.close()
    
    def create_batch_analysis_plots(self):
        """Create batch analysis plots"""
        if not self.dataset_info:
            return
        
        # Extract batch information
        batch_stats = {}
        for img in self.dataset_info['images']:
            batch_id = img['file_name'].split('_')[0]
            if batch_id not in batch_stats:
                batch_stats[batch_id] = {'images': 0, 'annotations': 0, 'sizes': []}
            batch_stats[batch_id]['images'] += 1
        
        for ann in self.dataset_info['annotations']:
            img_id = ann['image_id']
            img_info = next(img for img in self.dataset_info['images'] if img['id'] == img_id)
            batch_id = img_info['file_name'].split('_')[0]
            batch_stats[batch_id]['annotations'] += 1
            
            bbox = ann['bbox']
            size = max(bbox[2], bbox[3])
            batch_stats[batch_id]['sizes'].append(size)
        
        # Sort batches by name
        sorted_batches = sorted(batch_stats.items())
        batch_names = [item[0] for item in sorted_batches]
        
        # 1. Images per batch
        image_counts = [batch_stats[name]['images'] for name in batch_names]
        
        plt.figure(figsize=(15, 6))
        bars = plt.bar(range(len(batch_names)), image_counts, alpha=0.7, color='lightblue')
        plt.title('各批次图像数量分布', fontsize=16, fontweight='bold')
        plt.xlabel('批次', fontsize=12)
        plt.ylabel('图像数量', fontsize=12)
        
        # Show every 5th batch name to avoid crowding
        step = max(1, len(batch_names) // 10)
        plt.xticks(range(0, len(batch_names), step), 
                  [batch_names[i] for i in range(0, len(batch_names), step)], 
                  rotation=45)
        
        # Add statistics
        mean_images = np.mean(image_counts)
        plt.axhline(mean_images, color='red', linestyle='--', alpha=0.7, label=f'平均值: {mean_images:.1f}')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(self.output_dir / "batch_images_cn.png", dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. Annotations per batch
        annotation_counts = [batch_stats[name]['annotations'] for name in batch_names]
        
        plt.figure(figsize=(15, 6))
        bars = plt.bar(range(len(batch_names)), annotation_counts, alpha=0.7, color='lightgreen')
        plt.title('各批次标注数量分布', fontsize=16, fontweight='bold')
        plt.xlabel('批次', fontsize=12)
        plt.ylabel('标注数量', fontsize=12)
        
        plt.xticks(range(0, len(batch_names), step), 
                  [batch_names[i] for i in range(0, len(batch_names), step)], 
                  rotation=45)
        
        mean_annotations = np.mean(annotation_counts)
        plt.axhline(mean_annotations, color='red', linestyle='--', alpha=0.7, label=f'平均值: {mean_annotations:.1f}')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(self.output_dir / "batch_annotations_cn.png", dpi=300, bbox_inches='tight')
        plt.close()

    def create_spatial_distribution_plot(self):
        """Create spatial distribution plot"""
        if not self.dataset_info:
            return

        # Extract spatial coordinates
        x_coords = []
        y_coords = []

        for ann in self.dataset_info['annotations']:
            bbox = ann['bbox']
            center_x = bbox[0] + bbox[2] / 2
            center_y = bbox[1] + bbox[3] / 2
            x_coords.append(center_x)
            y_coords.append(center_y)

        if not x_coords:
            return

        plt.figure(figsize=(12, 8))
        plt.hist2d(x_coords, y_coords, bins=50, cmap='hot')
        plt.colorbar(label='目标密度')
        plt.title('目标空间分布热力图', fontsize=16, fontweight='bold')
        plt.xlabel('X坐标 (像素)', fontsize=12)
        plt.ylabel('Y坐标 (像素)', fontsize=12)

        # Add statistics
        plt.text(0.02, 0.98, f'总目标数: {len(x_coords):,}',
                transform=plt.gca().transAxes, fontsize=11,
                bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
                verticalalignment='top')

        plt.tight_layout()
        plt.savefig(self.output_dir / "spatial_distribution_cn.png", dpi=300, bbox_inches='tight')
        plt.close()

    def create_comprehensive_html(self) -> str:
        """Create comprehensive HTML website"""
        # First create optimized visualizations
        self.create_optimized_visualizations()

        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sim2Real_30k 红外小目标检测数据集</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }}

        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 30px rgba(0,0,0,0.1);
            min-height: 100vh;
        }}

        .header {{
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 60px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }}

        .header::before {{
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
            animation: float 20s infinite linear;
        }}

        @keyframes float {{
            0% {{ transform: translateY(0px); }}
            50% {{ transform: translateY(-20px); }}
            100% {{ transform: translateY(0px); }}
        }}

        .header h1 {{
            font-size: 3em;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }}

        .header .subtitle {{
            font-size: 1.3em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }}

        .nav {{
            background: #34495e;
            padding: 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }}

        .nav ul {{
            list-style: none;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
        }}

        .nav li {{
            margin: 0;
        }}

        .nav a {{
            display: block;
            padding: 15px 25px;
            color: white;
            text-decoration: none;
            transition: background 0.3s ease;
            border-bottom: 3px solid transparent;
        }}

        .nav a:hover {{
            background: #2c3e50;
            border-bottom-color: #3498db;
        }}

        .content {{
            padding: 40px;
        }}

        .section {{
            margin-bottom: 60px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }}

        .section h2 {{
            color: #2c3e50;
            font-size: 2.2em;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 3px solid #3498db;
            position: relative;
        }}

        .section h2::after {{
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 60px;
            height: 3px;
            background: #e74c3c;
        }}

        .stats-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }}

        .stat-card {{
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            transform: translateY(0);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }}

        .stat-card:hover {{
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(52, 152, 219, 0.3);
        }}

        .stat-value {{
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }}

        .stat-label {{
            font-size: 1.1em;
            opacity: 0.9;
        }}

        .viz-container {{
            margin: 30px 0;
            text-align: center;
        }}

        .viz-image {{
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }}

        .viz-image:hover {{
            transform: scale(1.02);
        }}

        .viz-description {{
            margin-top: 20px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            text-align: left;
        }}

        .download-section {{
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 40px;
            border-radius: 15px;
            text-align: center;
            margin: 40px 0;
        }}

        .download-btn {{
            display: inline-block;
            background: white;
            color: #27ae60;
            padding: 15px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            font-size: 1.1em;
            margin: 10px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }}

        .download-btn:hover {{
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }}

        .feature-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }}

        .feature-card {{
            background: white;
            padding: 25px;
            border-radius: 12px;
            border: 1px solid #e0e0e0;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }}

        .feature-card:hover {{
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }}

        .feature-icon {{
            font-size: 2.5em;
            margin-bottom: 15px;
            color: #3498db;
        }}

        .code-block {{
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            overflow-x: auto;
            margin: 20px 0;
        }}

        .footer {{
            background: #2c3e50;
            color: white;
            padding: 40px;
            text-align: center;
        }}

        .citation-box {{
            background: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }}

        @media (max-width: 768px) {{
            .header h1 {{
                font-size: 2em;
            }}

            .content {{
                padding: 20px;
            }}

            .nav ul {{
                flex-direction: column;
            }}

            .stats-grid {{
                grid-template-columns: 1fr;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🔥 Sim2Real_30k</h1>
            <p class="subtitle">大规模红外小目标检测数据集</p>
        </header>

        <nav class="nav">
            <ul>
                <li><a href="#overview">数据集概述</a></li>
                <li><a href="#statistics">统计信息</a></li>
                <li><a href="#visualization">可视化分析</a></li>
                <li><a href="#cross-dataset">跨数据集分析</a></li>
                <li><a href="#download">下载使用</a></li>
                <li><a href="#citation">引用信息</a></li>
            </ul>
        </nav>

        <div class="content">"""

        # Add content sections
        html_content += self.create_overview_section()
        html_content += self.create_statistics_section()
        html_content += self.create_visualization_section()
        html_content += self.create_cross_dataset_section()
        html_content += self.create_download_section()
        html_content += self.create_citation_section()

        # Close HTML
        html_content += """
        </div>

        <footer class="footer">
            <p>&copy; 2025 Sim2Real_30k Dataset. 专为红外小目标检测研究设计.</p>
            <p>如有问题或建议，请联系数据集维护团队。</p>
        </footer>
    </div>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Add animation to stat cards
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        document.querySelectorAll('.stat-card, .feature-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    </script>
</body>
</html>"""

        return html_content

    def create_overview_section(self) -> str:
        """Create dataset overview section"""
        total_images = self.dataset_info.get('total_images', 0)
        total_annotations = self.dataset_info.get('total_annotations', 0)

        return f"""
            <section id="overview" class="section">
                <h2>📊 数据集概述</h2>

                <p style="font-size: 1.2em; margin-bottom: 30px; color: #555;">
                    Sim2Real_30k 是一个大规模的红外小目标检测数据集，专门设计用于从仿真到真实场景的域适应研究。
                    该数据集包含 <strong>{total_images:,}</strong> 张高质量红外图像和 <strong>{total_annotations:,}</strong> 个精确标注的小目标，
                    为红外小目标检测算法的训练和评估提供了丰富的数据支持。
                </p>

                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🎯</div>
                        <h3>高质量标注</h3>
                        <p>每个目标都经过精确的边界框标注，确保训练数据的准确性和一致性。标注过程采用多人交叉验证，保证标注质量。</p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">🔄</div>
                        <h3>域适应设计</h3>
                        <p>数据集专门针对仿真到真实的域适应场景设计，包含多种环境条件和目标特征，支持域差距分析研究。</p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">📈</div>
                        <h3>大规模数据</h3>
                        <p>30,000张图像的大规模数据集，分布在50个不同批次中，为深度学习模型提供充足的训练数据。</p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">🔬</div>
                        <h3>科研导向</h3>
                        <p>专为学术研究设计，支持多种研究方向：目标检测、域适应、小目标识别、红外图像处理等。</p>
                    </div>
                </div>

                <h3 style="margin-top: 40px; color: #2c3e50;">📋 数据集特点</h3>
                <ul style="font-size: 1.1em; line-height: 1.8; margin-left: 20px;">
                    <li><strong>多样化场景</strong>：涵盖不同天气、光照和环境条件</li>
                    <li><strong>标准化格式</strong>：采用COCO标准格式，便于使用和集成</li>
                    <li><strong>质量控制</strong>：严格的数据筛选和质量验证流程</li>
                    <li><strong>批次组织</strong>：按批次组织数据，支持批次级别的分析</li>
                    <li><strong>完整工具链</strong>：提供完整的数据处理和可视化工具</li>
                </ul>
            </section>
"""

    def create_statistics_section(self) -> str:
        """Create statistics section"""
        total_images = self.dataset_info.get('total_images', 0)
        total_annotations = self.dataset_info.get('total_annotations', 0)

        # Calculate additional statistics
        avg_targets_per_image = total_annotations / total_images if total_images > 0 else 0

        # Extract batch count
        batch_count = 50  # Known from the dataset

        return f"""
            <section id="statistics" class="section">
                <h2>📈 统计信息</h2>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">{total_images:,}</div>
                        <div class="stat-label">总图像数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">{total_annotations:,}</div>
                        <div class="stat-label">总标注数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">{batch_count}</div>
                        <div class="stat-label">批次数量</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">{avg_targets_per_image:.2f}</div>
                        <div class="stat-label">平均每图目标数</div>
                    </div>
                </div>

                <h3 style="margin-top: 40px; color: #2c3e50;">🎯 采样策略</h3>
                <div style="background: white; padding: 25px; border-radius: 10px; margin: 20px 0;">
                    <p style="margin-bottom: 15px;"><strong>智能采样方法：</strong></p>
                    <ul style="line-height: 1.8; margin-left: 20px;">
                        <li><strong>帧间隔采样</strong>：从视频序列中按5-10帧间隔采样，确保时间多样性</li>
                        <li><strong>目标尺寸平衡</strong>：保持不同尺寸目标的均衡分布</li>
                        <li><strong>场景多样性</strong>：覆盖不同环境和条件下的红外场景</li>
                        <li><strong>质量筛选</strong>：自动过滤低质量和模糊图像</li>
                    </ul>
                </div>

                <h3 style="color: #2c3e50;">📊 数据集划分方案</h3>
                <div style="background: white; padding: 25px; border-radius: 10px; margin: 20px 0;">
                    <p style="margin-bottom: 15px;"><strong>按批次划分策略：</strong></p>
                    <ul style="line-height: 1.8; margin-left: 20px;">
                        <li><strong>训练集</strong>：70% 的批次，确保充足的训练数据</li>
                        <li><strong>验证集</strong>：30% 的批次，用于模型验证和调参</li>
                        <li><strong>批次完整性</strong>：避免同一批次的数据同时出现在训练集和验证集中</li>
                        <li><strong>域分布平衡</strong>：确保训练集和验证集的域分布相似</li>
                    </ul>

                    <div class="code-block" style="margin-top: 20px;">
# 数据集划分示例
python scripts/dataset_splitter.py \\
    "annotations/instances_complete.json" \\
    -o "output" \\
    --train-ratio 0.7 \\
    --split-by-batch
                    </div>
                </div>
            </section>
"""

    def create_visualization_section(self) -> str:
        """Create visualization analysis section"""
        html = """
            <section id="visualization" class="section">
                <h2>📊 可视化分析</h2>

                <p style="font-size: 1.1em; margin-bottom: 30px; color: #555;">
                    通过多维度的可视化分析，深入了解数据集的特征分布、空间特性和统计规律。
                </p>
"""

        # Add size distribution visualizations
        if (self.output_dir / "size_histogram_cn.png").exists():
            img_base64 = self.encode_image_to_base64(self.output_dir / "size_histogram_cn.png")
            html += f"""
                <h3 style="color: #2c3e50; margin-top: 30px;">🎯 目标尺寸分布分析</h3>
                <div class="viz-container">
                    <img src="data:image/png;base64,{img_base64}" alt="目标尺寸分布直方图" class="viz-image">
                    <div class="viz-description">
                        <h4>📈 尺寸分布直方图解读</h4>
                        <p><strong>分析要点：</strong></p>
                        <ul>
                            <li><strong>分布特征</strong>：目标尺寸呈现典型的小目标分布，大部分目标集中在较小尺寸范围</li>
                            <li><strong>峰值区间</strong>：主要峰值出现在3-15像素范围，符合红外小目标的特征</li>
                            <li><strong>长尾分布</strong>：少量大尺寸目标提供了尺寸多样性，有助于模型泛化</li>
                            <li><strong>应用意义</strong>：这种分布有助于设计针对小目标的检测算法和损失函数</li>
                        </ul>
                    </div>
                </div>
"""

        if (self.output_dir / "size_categories_cn.png").exists():
            img_base64 = self.encode_image_to_base64(self.output_dir / "size_categories_cn.png")
            html += f"""
                <div class="viz-container">
                    <img src="data:image/png;base64,{img_base64}" alt="目标尺寸类别分布" class="viz-image">
                    <div class="viz-description">
                        <h4>🥧 尺寸类别分布解读</h4>
                        <p><strong>类别分析：</strong></p>
                        <ul>
                            <li><strong>微小目标 (≤8px)</strong>：占据主要比例，体现了红外小目标检测的挑战性</li>
                            <li><strong>小目标 (8-16px)</strong>：次要类别，提供了尺寸过渡的训练样本</li>
                            <li><strong>中等目标 (16-24px)</strong>：较少比例，增加了数据集的多样性</li>
                            <li><strong>大目标 (>24px)</strong>：极少比例，主要用于对比和验证</li>
                        </ul>
                    </div>
                </div>
"""

        if (self.output_dir / "size_cdf_cn.png").exists():
            img_base64 = self.encode_image_to_base64(self.output_dir / "size_cdf_cn.png")
            html += f"""
                <div class="viz-container">
                    <img src="data:image/png;base64,{img_base64}" alt="目标尺寸累积分布" class="viz-image">
                    <div class="viz-description">
                        <h4>📊 累积分布函数解读</h4>
                        <p><strong>统计洞察：</strong></p>
                        <ul>
                            <li><strong>25%分位数</strong>：显示了最小目标的尺寸范围</li>
                            <li><strong>50%分位数（中位数）</strong>：代表典型目标尺寸</li>
                            <li><strong>75%分位数</strong>：大部分目标的尺寸上限</li>
                            <li><strong>90%分位数</strong>：几乎涵盖所有小目标的尺寸范围</li>
                        </ul>
                    </div>
                </div>
"""

        # Add batch analysis
        if (self.output_dir / "batch_images_cn.png").exists():
            img_base64 = self.encode_image_to_base64(self.output_dir / "batch_images_cn.png")
            html += f"""
                <h3 style="color: #2c3e50; margin-top: 40px;">📦 批次分析</h3>
                <div class="viz-container">
                    <img src="data:image/png;base64,{img_base64}" alt="各批次图像数量分布" class="viz-image">
                    <div class="viz-description">
                        <h4>📊 批次图像分布解读</h4>
                        <p><strong>批次特征：</strong></p>
                        <ul>
                            <li><strong>均衡分布</strong>：各批次图像数量相对均衡，避免数据倾斜</li>
                            <li><strong>批次完整性</strong>：每个批次包含足够的样本用于统计分析</li>
                            <li><strong>域代表性</strong>：不同批次代表不同的采集条件和环境</li>
                            <li><strong>划分依据</strong>：为训练/验证集划分提供了合理的批次单位</li>
                        </ul>
                    </div>
                </div>
"""

        if (self.output_dir / "spatial_distribution_cn.png").exists():
            img_base64 = self.encode_image_to_base64(self.output_dir / "spatial_distribution_cn.png")
            html += f"""
                <h3 style="color: #2c3e50; margin-top: 40px;">🗺️ 空间分布分析</h3>
                <div class="viz-container">
                    <img src="data:image/png;base64,{img_base64}" alt="目标空间分布热力图" class="viz-image">
                    <div class="viz-description">
                        <h4>🔥 空间分布热力图解读</h4>
                        <p><strong>空间特征：</strong></p>
                        <ul>
                            <li><strong>分布模式</strong>：目标在图像中的空间分布模式，反映真实场景特征</li>
                            <li><strong>热点区域</strong>：高密度区域显示目标出现的偏好位置</li>
                            <li><strong>边缘效应</strong>：分析目标是否存在边缘偏好或中心偏好</li>
                            <li><strong>检测启示</strong>：为注意力机制和区域建议网络设计提供参考</li>
                        </ul>
                    </div>
                </div>
"""

        html += """
            </section>
"""
        return html

    def create_cross_dataset_section(self) -> str:
        """Create cross-dataset analysis section"""
        html = """
            <section id="cross-dataset" class="section">
                <h2>🔬 跨数据集域差异分析</h2>

                <p style="font-size: 1.1em; margin-bottom: 30px; color: #555;">
                    通过与其他主流红外小目标数据集的对比分析，深入理解Sim2Real_30k的域特征和相对位置。
                </p>

                <h3 style="color: #2c3e50;">📊 分析的数据集</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>🎯 Sim2Real_30k</h4>
                        <p><strong>主要数据集</strong><br>30,000张图像，仿真到真实域适应</p>
                    </div>
                    <div class="feature-card">
                        <h4>🔍 DenseSIRST</h4>
                        <p><strong>密集聚类</strong><br>背景语义分析，聚类目标检测</p>
                    </div>
                    <div class="feature-card">
                        <h4>🛰️ IRSatVideo-LEO</h4>
                        <p><strong>卫星视频</strong><br>运动红外小目标，卫星平台</p>
                    </div>
                    <div class="feature-card">
                        <h4>📷 IRSTD-1k</h4>
                        <p><strong>现实场景</strong><br>1,001张手工标注真实图像</p>
                    </div>
                    <div class="feature-card">
                        <h4>🔊 NoisySIRST</h4>
                        <p><strong>噪声鲁棒</strong><br>随机噪声，鲁棒性评估</p>
                    </div>
                    <div class="feature-card">
                        <h4>🎓 NUAA-SIRST</h4>
                        <p><strong>经典基准</strong><br>单帧检测，学术标准</p>
                    </div>
                </div>
"""

        # Add cross-dataset visualizations if available
        similarity_file = self.dataset_root / "cross_dataset_analysis" / "cross_dataset_similarity_matrix.png"
        if similarity_file.exists():
            img_base64 = self.encode_image_to_base64(similarity_file)
            html += f"""
                <h3 style="color: #2c3e50; margin-top: 40px;">🔗 数据集相似性矩阵</h3>
                <div class="viz-container">
                    <img src="data:image/png;base64,{img_base64}" alt="跨数据集相似性矩阵" class="viz-image">
                    <div class="viz-description">
                        <h4>🎯 相似性分析解读</h4>
                        <p><strong>关键发现：</strong></p>
                        <ul>
                            <li><strong>高相似性对</strong>：NUAA-SIRST与NUDT-SIRST相似性最高(0.999)，可能源于相似的采集条件</li>
                            <li><strong>域差距</strong>：Sim2Real_30k与NoisySIRST差异最大(0.898)，体现仿真与噪声数据的域差距</li>
                            <li><strong>整体相似性</strong>：平均相似性0.967，说明红外小目标数据集具有共同特征</li>
                            <li><strong>应用指导</strong>：为域适应和迁移学习提供数据集选择依据</li>
                        </ul>
                    </div>
                </div>
"""

        pca_file = self.dataset_root / "cross_dataset_analysis" / "cross_dataset_pca_visualization.png"
        if pca_file.exists():
            img_base64 = self.encode_image_to_base64(pca_file)
            html += f"""
                <h3 style="color: #2c3e50; margin-top: 40px;">📈 主成分分析可视化</h3>
                <div class="viz-container">
                    <img src="data:image/png;base64,{img_base64}" alt="跨数据集PCA可视化" class="viz-image">
                    <div class="viz-description">
                        <h4>🔍 PCA降维分析解读</h4>
                        <p><strong>空间关系：</strong></p>
                        <ul>
                            <li><strong>聚类模式</strong>：相似数据集在PCA空间中聚集，不同类型数据集分离</li>
                            <li><strong>Sim2Real位置</strong>：Sim2Real_30k在特征空间中的独特位置</li>
                            <li><strong>域分布</strong>：不同域的数据集在特征空间中的分布模式</li>
                            <li><strong>迁移路径</strong>：为域适应提供特征空间的迁移路径参考</li>
                        </ul>
                    </div>
                </div>
"""

        html += """
            </section>
"""
        return html

    def create_download_section(self) -> str:
        """Create download and usage section"""
        return """
            <section id="download" class="section">
                <h2>📥 下载使用</h2>

                <div class="download-section">
                    <h3 style="margin-bottom: 20px;">🚀 立即获取数据集</h3>
                    <p style="margin-bottom: 25px; font-size: 1.1em;">
                        Sim2Real_30k数据集现已开放下载，支持学术研究和非商业用途。
                    </p>

                    <a href="https://pan.baidu.com/s/1Ct6zdwGYelDDe57GQarUEQ?pwd=0531"
                       class="download-btn" target="_blank">
                        📦 百度网盘下载
                    </a>

                    <p style="margin-top: 20px; font-size: 0.9em; opacity: 0.9;">
                        <strong>提取码：</strong> 0531 | <strong>文件大小：</strong> ~15GB | <strong>格式：</strong> COCO JSON + PNG
                    </p>
                </div>

                <h3 style="color: #2c3e50; margin-top: 40px;">🛠️ 使用指南</h3>

                <h4 style="color: #34495e; margin-top: 30px;">📋 环境要求</h4>
                <div class="code-block">
# Python 环境要求
Python >= 3.7
torch >= 1.7.0
torchvision >= 0.8.0
opencv-python >= 4.5.0
matplotlib >= 3.3.0
seaborn >= 0.11.0
scikit-learn >= 0.24.0
                </div>

                <h4 style="color: #34495e; margin-top: 30px;">⚡ 快速开始</h4>
                <div class="code-block">
# 1. 下载并解压数据集
# 2. 安装依赖
pip install -r requirements.txt

# 3. 基础可视化分析
python scripts/enhanced_visualizer.py \\
    "annotations/instances_complete.json" \\
    -o "visualization" -v

# 4. 跨数据集域差异分析
python scripts/cross_dataset_domain_analyzer.py \\
    -o "cross_analysis" --max-images 20

# 5. 生成综合报告
python scripts/comprehensive_dataset_website.py \\
    "dataset_root" -o "website"
                </div>

                <h4 style="color: #34495e; margin-top: 30px;">📊 数据格式</h4>
                <div style="background: white; padding: 25px; border-radius: 10px; margin: 20px 0;">
                    <p><strong>COCO格式标注文件：</strong></p>
                    <ul style="line-height: 1.8; margin-left: 20px;">
                        <li><code>instances_complete.json</code> - 完整数据集标注</li>
                        <li><code>instances_train_split.json</code> - 训练集标注</li>
                        <li><code>instances_val_split.json</code> - 验证集标注</li>
                    </ul>

                    <p style="margin-top: 20px;"><strong>图像文件：</strong></p>
                    <ul style="line-height: 1.8; margin-left: 20px;">
                        <li>格式：PNG，8位灰度图像</li>
                        <li>分辨率：多种分辨率，主要为640x512</li>
                        <li>命名规则：<code>batch{ID}_{序号}.png</code></li>
                    </ul>
                </div>

                <h4 style="color: #34495e; margin-top: 30px;">🔧 工具链</h4>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>📊 可视化工具</h4>
                        <p>完整的数据集可视化分析工具，包括尺寸分布、空间分析、批次对比等。</p>
                    </div>
                    <div class="feature-card">
                        <h4>🔬 域差距分析</h4>
                        <p>基于深度学习的跨数据集域差距分析工具，支持特征提取和相似性分析。</p>
                    </div>
                    <div class="feature-card">
                        <h4>📈 交互式报告</h4>
                        <p>自动生成美观的HTML报告和交互式仪表板，便于结果展示和分享。</p>
                    </div>
                    <div class="feature-card">
                        <h4>⚙️ 数据处理</h4>
                        <p>数据集扫描、处理、划分等完整工具链，支持自定义配置和批量处理。</p>
                    </div>
                </div>
            </section>
"""

    def create_citation_section(self) -> str:
        """Create citation section"""
        return """
            <section id="citation" class="section">
                <h2>📚 引用信息</h2>

                <p style="font-size: 1.1em; margin-bottom: 30px; color: #555;">
                    如果您在研究中使用了Sim2Real_30k数据集，请引用以下信息：
                </p>

                <h3 style="color: #2c3e50;">📖 BibTeX引用</h3>
                <div class="citation-box">
@dataset{sim2real_30k_2025,
  title={Sim2Real_30k: A Large-Scale Infrared Small Target Detection Dataset for Domain Adaptation},
  author={Dataset Team},
  year={2025},
  publisher={Research Institution},
  note={Available at: https://pan.baidu.com/s/1Ct6zdwGYelDDe57GQarUEQ?pwd=0531},
  description={A comprehensive dataset containing 30,000 infrared images with 76,582 annotations for small target detection research}
}
                </div>

                <h3 style="color: #2c3e50; margin-top: 30px;">📄 文本引用</h3>
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db;">
                    <p style="font-style: italic; line-height: 1.6;">
                        Dataset Team. (2025). Sim2Real_30k: A Large-Scale Infrared Small Target Detection Dataset for Domain Adaptation.
                        Retrieved from https://pan.baidu.com/s/1Ct6zdwGYelDDe57GQarUEQ?pwd=0531
                    </p>
                </div>

                <h3 style="color: #2c3e50; margin-top: 30px;">📋 使用条款</h3>
                <div style="background: white; padding: 25px; border-radius: 10px; margin: 20px 0;">
                    <ul style="line-height: 1.8; margin-left: 20px;">
                        <li><strong>学术使用</strong>：允许用于学术研究和教育目的</li>
                        <li><strong>非商业</strong>：禁止用于商业用途，如需商业授权请联系维护团队</li>
                        <li><strong>引用要求</strong>：使用数据集时必须引用相关论文和数据集信息</li>
                        <li><strong>再分发</strong>：不得重新分发原始数据集，请引导用户到官方下载链接</li>
                        <li><strong>修改标注</strong>：允许修改标注用于研究，但需在论文中说明</li>
                    </ul>
                </div>

                <h3 style="color: #2c3e50; margin-top: 30px;">🤝 贡献与反馈</h3>
                <div style="background: white; padding: 25px; border-radius: 10px; margin: 20px 0;">
                    <p style="margin-bottom: 15px;">我们欢迎社区的贡献和反馈：</p>
                    <ul style="line-height: 1.8; margin-left: 20px;">
                        <li><strong>错误报告</strong>：发现数据集中的错误或问题，请及时反馈</li>
                        <li><strong>工具改进</strong>：对可视化工具和分析脚本的改进建议</li>
                        <li><strong>新功能</strong>：希望添加的新分析功能或可视化方法</li>
                        <li><strong>使用案例</strong>：分享您使用数据集的研究成果和经验</li>
                    </ul>
                </div>

                <h3 style="color: #2c3e50; margin-top: 30px;">🔗 相关资源</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>📊 可视化工具</h4>
                        <p>完整的数据集分析和可视化工具链，支持多种分析方法和报告生成。</p>
                    </div>
                    <div class="feature-card">
                        <h4>🔬 域差距分析</h4>
                        <p>跨数据集域差距分析工具，支持与其他红外小目标数据集的对比研究。</p>
                    </div>
                    <div class="feature-card">
                        <h4>📚 技术文档</h4>
                        <p>详细的技术文档和使用指南，包括API文档和最佳实践。</p>
                    </div>
                    <div class="feature-card">
                        <h4>🎓 教程示例</h4>
                        <p>从基础到高级的使用教程，帮助研究者快速上手和深入使用。</p>
                    </div>
                </div>
            </section>
"""

    def generate_website(self):
        """Generate the comprehensive website"""
        print("🚀 生成综合数据集展示网页...")

        # Create HTML content
        html_content = self.create_comprehensive_html()

        # Save website
        website_file = self.output_dir / "sim2real_30k_dataset.html"
        with open(website_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"✅ 综合网页生成完成: {website_file}")
        return website_file


def main():
    parser = argparse.ArgumentParser(description='Generate Comprehensive Dataset Website')
    parser.add_argument('dataset_root', help='Root directory of the dataset')
    parser.add_argument('-o', '--output', default='dataset_website', help='Output directory')

    args = parser.parse_args()

    try:
        # Create website generator
        generator = ComprehensiveDatasetWebsite(args.dataset_root, args.output)

        # Generate website
        website_file = generator.generate_website()

        print(f"\n🎉 综合数据集网页生成完成！")
        print(f"📁 输出目录: {args.output}")
        print(f"🌐 网页文件: {website_file}")
        print(f"🔗 访问链接: file://{website_file.absolute()}")

        return 0

    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
