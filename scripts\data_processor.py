#!/usr/bin/env python3
"""
数据处理管道 - 红外小目标检测数据集筛选和转换
"""

import os
import json
import random
import shutil
import argparse
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Tuple, Optional
from datetime import datetime

try:
    from tqdm import tqdm
except ImportError:
    print("警告: 未安装tqdm库，将使用简单进度显示")
    tqdm = None

class DataProcessor:
    def __init__(self, config: Dict):
        self.config = config
        self.stats = {
            'processed_batches': 0,
            'total_candidates': 0,
            'selected_images': 0,
            'size_distribution': defaultdict(int),
            'error_count': 0,
            'batch_stats': []
        }
        
    def load_scan_results(self, scan_file: str) -> Dict:
        """加载扫描结果"""
        print(f"加载扫描结果: {scan_file}")
        
        with open(scan_file, 'r', encoding='utf-8') as f:
            scan_data = json.load(f)
        
        print(f"✓ 扫描结果加载完成:")
        print(f"  - 有效batch数: {scan_data['valid_batches']}")
        print(f"  - 总图像对: {scan_data['total_pairs']}")
        print(f"  - 有效目标: {scan_data['valid_targets']}")
        
        return scan_data
    
    def calculate_batch_quotas(self, scan_data: Dict) -> Dict[str, int]:
        """计算每个batch的采样配额"""
        print("计算batch采样配额...")
        
        target_count = self.config['target_count']
        batch_quotas = {}
        total_valid_pairs = sum(batch['valid_pairs'] for batch in scan_data['batch_info'])
        
        for batch in scan_data['batch_info']:
            # 基于有效图像对数量分配配额
            quota = int((batch['valid_pairs'] / total_valid_pairs) * target_count)
            batch_quotas[batch['path']] = max(1, quota)  # 至少1张
        
        # 调整配额确保总数为target_count
        current_total = sum(batch_quotas.values())
        if current_total != target_count:
            # 按比例调整最大的几个batch
            sorted_batches = sorted(batch_quotas.items(), key=lambda x: x[1], reverse=True)
            diff = target_count - current_total
            
            for i, (batch_path, quota) in enumerate(sorted_batches):
                if diff == 0:
                    break
                if diff > 0:
                    batch_quotas[batch_path] += 1
                    diff -= 1
                else:
                    if quota > 1:
                        batch_quotas[batch_path] -= 1
                        diff += 1
        
        print(f"✓ 配额计算完成，总配额: {sum(batch_quotas.values())}")
        return batch_quotas
    
    def analyze_json_file(self, json_path: Path) -> Dict:
        """分析JSON文件，返回有效目标信息"""
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if not content:
                    return {'valid': False, 'error': 'empty_content'}
                
                data = json.loads(content)
            
            # 处理两种数据格式
            targets = []
            if isinstance(data, list):
                targets = data
            elif isinstance(data, dict) and 'units' in data:
                targets = data['units']
            else:
                return {'valid': False, 'error': 'unknown_format'}
            
            valid_targets = []
            for target in targets:
                if 'bbox' not in target:
                    continue
                
                # 对于旧格式，检查飞机类型和状态
                if 'm_blk_path' in target:
                    blk_path = target.get('m_blk_path', '')
                    if blk_path is None or 'gameData/flightModels/' not in blk_path:
                        continue
                    if target.get('isdead', True) or not target.get('isvisible', False):
                        continue
                
                # 计算目标尺寸
                bbox = target['bbox']
                if len(bbox) >= 4:
                    width = abs(bbox[2] - bbox[0])
                    height = abs(bbox[3] - bbox[1])
                    size = max(width, height)
                    
                    if 3 <= size <= 33:  # 尺寸筛选
                        valid_targets.append({
                            'bbox': bbox,
                            'size': size,
                            'id': target.get('id', 0)
                        })
            
            return {
                'valid': len(valid_targets) > 0,
                'targets': valid_targets,
                'target_count': len(valid_targets)
            }
            
        except Exception as e:
            return {'valid': False, 'error': str(e)}
    
    def get_size_category(self, size: float) -> str:
        """获取尺寸类别"""
        if size < 5:
            return "0-4px"
        elif size < 10:
            return "5-9px"
        elif size < 15:
            return "10-14px"
        elif size < 20:
            return "15-19px"
        elif size < 25:
            return "20-24px"
        elif size < 30:
            return "25-29px"
        else:
            return "30-34px"
    
    def find_image_label_pairs(self, folder_path: Path) -> List[Tuple[Path, Path]]:
        """在文件夹中查找图像-标签配对"""
        pairs = []
        
        # 获取所有png和json文件
        png_files = list(folder_path.glob("*.png"))
        json_files = list(folder_path.glob("*.json"))
        
        # 模式1: 相同基础名称
        for png_file in png_files:
            base_name = png_file.stem
            json_file = folder_path / f"{base_name}.json"
            if json_file.exists():
                pairs.append((png_file, json_file))
        
        # 模式2: output_images + video_label
        import re
        output_pattern = re.compile(r'output_images(\d+)\.png')
        for png_file in png_files:
            match = output_pattern.match(png_file.name)
            if match:
                num = match.group(1)
                json_file = folder_path / f"video_label{num}.json"
                if json_file.exists():
                    pairs.append((png_file, json_file))
        
        return pairs
    
    def sample_from_batch(self, batch_path: str, quota: int) -> List[Dict]:
        """从单个batch中采样"""
        folder_path = Path(batch_path)
        pairs = self.find_image_label_pairs(folder_path)
        
        if not pairs:
            return []
        
        # 分析所有图像-标签对
        candidates = []
        for img_path, json_path in pairs:
            analysis = self.analyze_json_file(json_path)
            
            if analysis['valid'] and analysis['target_count'] > 0:
                # 计算图像的代表性尺寸（最大目标的尺寸）
                max_size = max(target['size'] for target in analysis['targets'])
                size_category = self.get_size_category(max_size)
                
                candidates.append({
                    'img_path': img_path,
                    'json_path': json_path,
                    'targets': analysis['targets'],
                    'max_size': max_size,
                    'size_category': size_category,
                    'target_count': analysis['target_count']
                })
        
        if not candidates:
            return []
        
        # 如果候选数量少于配额，直接返回所有候选
        if len(candidates) <= quota:
            return candidates

        # 按尺寸分布进行分层采样
        size_groups = defaultdict(list)
        for candidate in candidates:
            size_groups[candidate['size_category']].append(candidate)

        selected = []
        remaining_quota = quota

        # 按配置的尺寸分布采样
        for size_cat, target_ratio in self.config['size_distribution'].items():
            if remaining_quota <= 0:
                break

            target_count = max(1, int(quota * target_ratio))  # 至少1个
            available = size_groups.get(size_cat, [])

            if available:
                # 随机采样，但保持时序间隔
                available.sort(key=lambda x: x['img_path'].name)  # 按文件名排序

                # 应用时序间隔
                interval = max(1, min(len(available), random.randint(*self.config['frame_interval'])))
                sampled = available[::interval]

                # 如果采样不足，补充采样
                if len(sampled) < target_count and len(available) > len(sampled):
                    additional_needed = min(target_count - len(sampled),
                                          len(available) - len(sampled))
                    remaining = [x for x in available if x not in sampled]
                    if remaining:
                        additional = random.sample(remaining, min(additional_needed, len(remaining)))
                        sampled.extend(additional)

                # 限制在目标数量内
                sampled = sampled[:min(target_count, remaining_quota)]
                selected.extend(sampled)
                remaining_quota -= len(sampled)

        # 如果还有剩余配额，从所有候选中随机选择
        if remaining_quota > 0:
            all_unselected = [c for c in candidates if c not in selected]
            if all_unselected:
                additional = random.sample(all_unselected, min(remaining_quota, len(all_unselected)))
                selected.extend(additional)

        return selected[:quota]  # 确保不超过配额

    def get_image_size(self, img_path: Path) -> Tuple[int, int]:
        """获取图像尺寸"""
        try:
            from PIL import Image
            with Image.open(img_path) as img:
                return img.size  # (width, height)
        except ImportError:
            # 如果没有PIL，使用默认尺寸
            return (1920, 1080)
        except Exception:
            # 如果读取失败，使用默认尺寸
            return (1920, 1080)

    def create_coco_annotation(self, selected_data: List[Dict], output_dir: Path) -> Dict:
        """创建COCO格式标注"""
        print("生成COCO格式标注...")

        coco_data = {
            "info": {
                "description": "红外小目标检测数据集",
                "version": "1.0",
                "year": datetime.now().year,
                "contributor": "Sim2Real Project",
                "date_created": datetime.now().isoformat()
            },
            "licenses": [
                {
                    "id": 1,
                    "name": "Custom License",
                    "url": ""
                }
            ],
            "categories": [
                {
                    "id": 1,
                    "name": "aircraft",
                    "supercategory": "vehicle"
                }
            ],
            "images": [],
            "annotations": []
        }

        image_id = 1
        annotation_id = 1

        for item in selected_data:
            # 添加图像信息
            img_path = item['img_path']
            width, height = self.get_image_size(img_path)

            image_info = {
                "id": image_id,
                "width": width,
                "height": height,
                "file_name": f"batch{item['batch_id']}_{image_id:06d}.png",
                "license": 1,
                "date_captured": datetime.now().isoformat()
            }
            coco_data["images"].append(image_info)

            # 添加标注信息
            for target in item['targets']:
                bbox = target['bbox']
                # 转换为COCO格式 [x, y, width, height]
                x1, y1, x2, y2 = bbox
                bbox_width = abs(x2 - x1)
                bbox_height = abs(y2 - y1)
                area = bbox_width * bbox_height

                annotation = {
                    "id": annotation_id,
                    "image_id": image_id,
                    "category_id": 1,  # aircraft
                    "bbox": [min(x1, x2), min(y1, y2), bbox_width, bbox_height],
                    "area": area,
                    "iscrowd": 0,
                    "segmentation": []  # 暂时为空，如果需要可以添加
                }
                coco_data["annotations"].append(annotation)
                annotation_id += 1

            image_id += 1

        return coco_data

    def copy_selected_images(self, selected_data: List[Dict], output_dir: Path) -> None:
        """复制选中的图像文件"""
        images_dir = output_dir / "images"
        images_dir.mkdir(parents=True, exist_ok=True)

        print(f"复制图像文件到: {images_dir}")

        if tqdm is not None:
            iterator = tqdm(selected_data, desc="复制图像", unit="image")
        else:
            iterator = selected_data
            print(f"开始复制 {len(selected_data)} 张图像...")

        for i, item in enumerate(iterator):
            src_path = item['img_path']
            dst_name = f"batch{item['batch_id']}_{i+1:06d}.png"
            dst_path = images_dir / dst_name

            try:
                shutil.copy2(src_path, dst_path)
                item['output_filename'] = dst_name  # 记录输出文件名
            except Exception as e:
                print(f"复制文件失败 {src_path}: {e}")
                self.stats['error_count'] += 1

    def generate_reports(self, selected_data: List[Dict], output_dir: Path) -> None:
        """生成处理报告"""
        reports_dir = output_dir / "reports"
        reports_dir.mkdir(parents=True, exist_ok=True)

        # 采样报告
        sampling_report = {
            "summary": {
                "total_selected": len(selected_data),
                "target_count": self.config['target_count'],
                "processed_batches": self.stats['processed_batches'],
                "error_count": self.stats['error_count']
            },
            "size_distribution": dict(self.stats['size_distribution']),
            "batch_stats": self.stats['batch_stats'],
            "config": self.config
        }

        with open(reports_dir / "sampling_report.json", 'w', encoding='utf-8') as f:
            json.dump(sampling_report, f, ensure_ascii=False, indent=2)

        # 质量报告
        quality_report = {
            "data_quality": {
                "total_candidates": self.stats['total_candidates'],
                "selected_images": self.stats['selected_images'],
                "selection_rate": self.stats['selected_images'] / max(1, self.stats['total_candidates']),
                "error_rate": self.stats['error_count'] / max(1, self.stats['selected_images'])
            },
            "size_analysis": {
                "target_distribution": self.config['size_distribution'],
                "actual_distribution": {
                    k: v / max(1, sum(self.stats['size_distribution'].values()))
                    for k, v in self.stats['size_distribution'].items()
                }
            }
        }

        with open(reports_dir / "quality_report.json", 'w', encoding='utf-8') as f:
            json.dump(quality_report, f, ensure_ascii=False, indent=2)

        print(f"✓ 报告已生成: {reports_dir}")

    def process_dataset(self, scan_file: str, output_dir: str) -> None:
        """主处理流程"""
        print("="*80)
        print("红外小目标检测数据集处理管道")
        print("="*80)

        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        # 1. 加载扫描结果
        scan_data = self.load_scan_results(scan_file)

        # 2. 计算batch配额
        batch_quotas = self.calculate_batch_quotas(scan_data)

        # 3. 处理每个batch
        all_selected = []
        batch_id = 1

        print(f"\n开始处理 {len(batch_quotas)} 个batch...")

        for batch_path, quota in batch_quotas.items():
            if quota <= 0:
                continue

            print(f"\n处理Batch {batch_id}: {Path(batch_path).name}")
            print(f"配额: {quota}")

            selected = self.sample_from_batch(batch_path, quota)

            # 添加batch_id到每个选中项
            for item in selected:
                item['batch_id'] = batch_id
                item['batch_path'] = batch_path

            all_selected.extend(selected)

            # 更新统计
            self.stats['processed_batches'] += 1
            self.stats['selected_images'] += len(selected)

            for item in selected:
                size_cat = item['size_category']
                self.stats['size_distribution'][size_cat] += 1

            batch_stats = {
                'batch_id': batch_id,
                'batch_path': batch_path,
                'quota': quota,
                'selected': len(selected),
                'selection_rate': len(selected) / quota if quota > 0 else 0
            }
            self.stats['batch_stats'].append(batch_stats)

            print(f"✓ 已选择 {len(selected)} 张图像")
            batch_id += 1

        print(f"\n总共选择了 {len(all_selected)} 张图像")

        # 4. 生成COCO标注
        coco_data = self.create_coco_annotation(all_selected, output_path)

        annotations_dir = output_path / "annotations"
        annotations_dir.mkdir(parents=True, exist_ok=True)

        with open(annotations_dir / "instances_train.json", 'w', encoding='utf-8') as f:
            json.dump(coco_data, f, ensure_ascii=False, indent=2)

        print(f"✓ COCO标注已保存: {annotations_dir / 'instances_train.json'}")

        # 5. 复制图像文件
        self.copy_selected_images(all_selected, output_path)

        # 6. 生成报告
        self.generate_reports(all_selected, output_path)

        print(f"\n{'='*80}")
        print("处理完成！")
        print(f"输出目录: {output_path}")
        print(f"图像数量: {len(all_selected)}")
        print(f"存储大小: 约 {len(all_selected) * 8 / 1024:.1f} GB")
        print(f"{'='*80}")

def main():
    parser = argparse.ArgumentParser(description="红外小目标检测数据集处理管道")
    parser.add_argument('scan_file', help='扫描结果文件路径')
    parser.add_argument('-o', '--output', default='output', help='输出目录')
    parser.add_argument('-n', '--count', type=int, default=30000, help='目标图像数量')
    parser.add_argument('--config', help='配置文件路径')

    args = parser.parse_args()

    # 默认配置
    config = {
        "target_count": args.count,
        "frame_interval": (5, 10),
        "random_offset": 2,
        "size_distribution": {
            "0-4px": 0.25,
            "5-9px": 0.35,
            "10-14px": 0.20,
            "15-19px": 0.10,
            "20-24px": 0.06,
            "25-29px": 0.03,
            "30-34px": 0.01
        },
        "quality_threshold": 0.8,
        "max_errors_per_batch": 10
    }

    # 如果提供了配置文件，加载配置
    if args.config and Path(args.config).exists():
        with open(args.config, 'r', encoding='utf-8') as f:
            user_config = json.load(f)
            config.update(user_config)

    # 创建处理器并运行
    processor = DataProcessor(config)
    processor.process_dataset(args.scan_file, args.output)

if __name__ == "__main__":
    main()
