#!/usr/bin/env python3
"""
Enhanced Dataset Visualizer with Progress Tracking and Error Handling
Improved version of the dataset visualizer with better UX
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Tuple, Optional
import argparse
import sys
import time
from datetime import datetime

# Try to import tqdm for progress bars
try:
    from tqdm import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False
    print("📝 Note: Install 'tqdm' for progress bars: pip install tqdm")

# Set font and style
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
sns.set_palette("husl")

class ProgressTracker:
    """Simple progress tracker when tqdm is not available"""
    def __init__(self, total: int, desc: str = "Processing"):
        self.total = total
        self.current = 0
        self.desc = desc
        self.start_time = time.time()
        
    def update(self, n: int = 1):
        self.current += n
        if self.current % max(1, self.total // 20) == 0 or self.current == self.total:
            percent = (self.current / self.total) * 100
            elapsed = time.time() - self.start_time
            rate = self.current / elapsed if elapsed > 0 else 0
            print(f"\r{self.desc}: {percent:.1f}% ({self.current}/{self.total}) [{rate:.1f}it/s]", end="")
            if self.current == self.total:
                print()  # New line when complete
    
    def __enter__(self):
        return self
    
    def __exit__(self, *args):
        pass

def create_progress_bar(total: int, desc: str = "Processing"):
    """Create progress bar (tqdm if available, otherwise simple tracker)"""
    if TQDM_AVAILABLE:
        return tqdm(total=total, desc=desc, unit="item")
    else:
        return ProgressTracker(total, desc)

class EnhancedDatasetVisualizer:
    def __init__(self, annotation_file: str, output_dir: str, verbose: bool = True):
        self.annotation_file = Path(annotation_file)
        self.output_dir = Path(output_dir)
        self.verbose = verbose
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Validation
        if not self.annotation_file.exists():
            raise FileNotFoundError(f"Annotation file not found: {annotation_file}")
        
        # Load and validate data
        self.coco_data = self.load_and_validate_data()
        self.analysis_data = None
        
    def log(self, message: str, level: str = "INFO"):
        """Enhanced logging with timestamps"""
        if self.verbose:
            timestamp = datetime.now().strftime("%H:%M:%S")
            icons = {"INFO": "ℹ️", "SUCCESS": "✅", "WARNING": "⚠️", "ERROR": "❌"}
            icon = icons.get(level, "📝")
            print(f"[{timestamp}] {icon} {message}")
    
    def load_and_validate_data(self) -> Dict:
        """Load and validate COCO format data with error handling"""
        self.log(f"Loading dataset: {self.annotation_file}")
        
        try:
            with open(self.annotation_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON format: {e}")
        except Exception as e:
            raise IOError(f"Error reading file: {e}")
        
        # Validate required fields
        required_fields = ['images', 'annotations', 'categories']
        for field in required_fields:
            if field not in data:
                raise ValueError(f"Missing required field: {field}")
        
        # Basic validation
        if not data['images']:
            raise ValueError("No images found in dataset")
        if not data['annotations']:
            raise ValueError("No annotations found in dataset")
        
        self.log(f"Dataset loaded successfully:", "SUCCESS")
        self.log(f"  📸 Images: {len(data['images']):,}")
        self.log(f"  🎯 Annotations: {len(data['annotations']):,}")
        self.log(f"  📂 Categories: {len(data['categories'])}")
        
        return data
    
    def analyze_dataset_with_progress(self) -> Dict:
        """Analyze dataset with progress tracking"""
        self.log("Starting comprehensive dataset analysis...")
        
        images = self.coco_data['images']
        annotations = self.coco_data['annotations']
        
        # Initialize analysis data
        analysis = {
            'batch_stats': defaultdict(lambda: {
                'image_count': 0,
                'annotation_count': 0,
                'target_sizes': [],
                'target_densities': []
            }),
            'size_distribution': defaultdict(int),
            'spatial_distribution': [],
            'total_stats': {}
        }
        
        # Process images with progress
        self.log("Processing images...")
        with create_progress_bar(len(images), "Analyzing images") as pbar:
            for img in images:
                try:
                    # Extract batch info
                    batch_id = self.extract_batch_id(img['file_name'])
                    analysis['batch_stats'][batch_id]['image_count'] += 1
                    
                    if TQDM_AVAILABLE:
                        pbar.update(1)
                    else:
                        pbar.update(1)
                        
                except Exception as e:
                    self.log(f"Error processing image {img.get('id', 'unknown')}: {e}", "WARNING")
                    continue
        
        # Process annotations with progress
        self.log("Processing annotations...")
        with create_progress_bar(len(annotations), "Analyzing annotations") as pbar:
            for ann in annotations:
                try:
                    # Find corresponding image
                    img_info = next((img for img in images if img['id'] == ann['image_id']), None)
                    if not img_info:
                        continue
                    
                    batch_id = self.extract_batch_id(img_info['file_name'])
                    
                    # Calculate target size
                    bbox = ann['bbox']
                    target_size = max(bbox[2], bbox[3])  # max of width, height
                    
                    # Update statistics
                    analysis['batch_stats'][batch_id]['annotation_count'] += 1
                    analysis['batch_stats'][batch_id]['target_sizes'].append(target_size)
                    
                    # Size distribution
                    size_category = self.categorize_size(target_size)
                    analysis['size_distribution'][size_category] += 1
                    
                    # Spatial distribution
                    center_x = bbox[0] + bbox[2] / 2
                    center_y = bbox[1] + bbox[3] / 2
                    analysis['spatial_distribution'].append((center_x, center_y))
                    
                    if TQDM_AVAILABLE:
                        pbar.update(1)
                    else:
                        pbar.update(1)
                        
                except Exception as e:
                    self.log(f"Error processing annotation {ann.get('id', 'unknown')}: {e}", "WARNING")
                    continue
        
        # Calculate summary statistics
        all_sizes = []
        for batch_data in analysis['batch_stats'].values():
            all_sizes.extend(batch_data['target_sizes'])
        
        if all_sizes:
            analysis['total_stats'] = {
                'total_batches': len(analysis['batch_stats']),
                'avg_targets_per_image': len(annotations) / len(images),
                'size_range': (min(all_sizes), max(all_sizes)),
                'size_mean': np.mean(all_sizes),
                'size_std': np.std(all_sizes)
            }
        
        self.log("Analysis completed successfully!", "SUCCESS")
        return dict(analysis)
    
    def extract_batch_id(self, filename: str) -> str:
        """Extract batch ID from filename with error handling"""
        try:
            return filename.split('_')[0]
        except:
            return 'unknown'
    
    def categorize_size(self, size: float) -> str:
        """Categorize target size into ranges"""
        if size <= 8:
            return "Tiny (≤8px)"
        elif size <= 16:
            return "Small (8-16px)"
        elif size <= 24:
            return "Medium (16-24px)"
        else:
            return "Large (>24px)"

    def generate_enhanced_visualizations(self):
        """Generate all visualizations with progress tracking and error handling"""
        if not self.analysis_data:
            self.analysis_data = self.analyze_dataset_with_progress()

        visualizations = [
            ("Size Distribution Analysis", self.create_size_distribution_plot),
            ("Batch Comparison Analysis", self.create_batch_comparison_plot),
            ("Spatial Distribution Heatmap", self.create_spatial_distribution_plot),
            ("Dataset Summary Report", self.create_summary_report)
        ]

        self.log("Starting visualization generation...")

        with create_progress_bar(len(visualizations), "Generating visualizations") as pbar:
            for viz_name, viz_func in visualizations:
                try:
                    self.log(f"Creating {viz_name}...")
                    viz_func()
                    self.log(f"✓ {viz_name} completed", "SUCCESS")
                except Exception as e:
                    self.log(f"✗ Error creating {viz_name}: {e}", "ERROR")

                if TQDM_AVAILABLE:
                    pbar.update(1)
                else:
                    pbar.update(1)

        self.log("All visualizations completed!", "SUCCESS")

    def create_size_distribution_plot(self):
        """Create enhanced size distribution plot"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Target Size Distribution Analysis', fontsize=16, fontweight='bold')

        # Get all target sizes
        all_sizes = []
        for batch_data in self.analysis_data['batch_stats'].values():
            all_sizes.extend(batch_data['target_sizes'])

        if not all_sizes:
            self.log("No target sizes found for visualization", "WARNING")
            return

        # 1. Histogram
        ax1.hist(all_sizes, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.set_title('Size Distribution Histogram')
        ax1.set_xlabel('Target Size (pixels)')
        ax1.set_ylabel('Frequency')
        ax1.grid(True, alpha=0.3)

        # 2. Box plot
        ax2.boxplot(all_sizes, vert=True)
        ax2.set_title('Size Distribution Box Plot')
        ax2.set_ylabel('Target Size (pixels)')
        ax2.grid(True, alpha=0.3)

        # 3. Pie chart by categories
        size_counts = dict(self.analysis_data['size_distribution'])
        if size_counts:
            ax3.pie(size_counts.values(), labels=size_counts.keys(), autopct='%1.1f%%', startangle=90)
            ax3.set_title('Size Category Distribution')

        # 4. CDF
        sorted_sizes = np.sort(all_sizes)
        y = np.arange(1, len(sorted_sizes) + 1) / len(sorted_sizes)
        ax4.plot(sorted_sizes, y, linewidth=2, color='red')
        ax4.set_title('Cumulative Distribution Function')
        ax4.set_xlabel('Target Size (pixels)')
        ax4.set_ylabel('Cumulative Probability')
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        output_file = self.output_dir / "enhanced_size_distribution.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()

    def create_batch_comparison_plot(self):
        """Create enhanced batch comparison plot"""
        batch_stats = self.analysis_data['batch_stats']

        if not batch_stats:
            self.log("No batch statistics found for visualization", "WARNING")
            return

        # Prepare data
        batch_ids = list(batch_stats.keys())
        image_counts = [batch_stats[bid]['image_count'] for bid in batch_ids]
        annotation_counts = [batch_stats[bid]['annotation_count'] for bid in batch_ids]
        avg_sizes = [np.mean(batch_stats[bid]['target_sizes']) if batch_stats[bid]['target_sizes'] else 0
                    for bid in batch_ids]

        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(16, 12))
        fig.suptitle('Inter-Batch Comparison Analysis', fontsize=16, fontweight='bold')

        # 1. Image counts
        bars1 = ax1.bar(range(len(batch_ids)), image_counts, alpha=0.7, color='lightblue')
        ax1.set_title('Images per Batch')
        ax1.set_ylabel('Image Count')
        ax1.set_xticks(range(0, len(batch_ids), max(1, len(batch_ids)//10)))
        ax1.grid(True, alpha=0.3)

        # 2. Annotation counts
        bars2 = ax2.bar(range(len(batch_ids)), annotation_counts, alpha=0.7, color='lightgreen')
        ax2.set_title('Annotations per Batch')
        ax2.set_ylabel('Annotation Count')
        ax2.set_xticks(range(0, len(batch_ids), max(1, len(batch_ids)//10)))
        ax2.grid(True, alpha=0.3)

        # 3. Average target sizes
        bars3 = ax3.bar(range(len(batch_ids)), avg_sizes, alpha=0.7, color='lightcoral')
        ax3.set_title('Average Target Size per Batch')
        ax3.set_xlabel('Batch Index')
        ax3.set_ylabel('Average Size (pixels)')
        ax3.set_xticks(range(0, len(batch_ids), max(1, len(batch_ids)//10)))
        ax3.grid(True, alpha=0.3)

        plt.tight_layout()
        output_file = self.output_dir / "enhanced_batch_comparison.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()

    def create_spatial_distribution_plot(self):
        """Create spatial distribution heatmap"""
        spatial_data = self.analysis_data['spatial_distribution']

        if not spatial_data:
            self.log("No spatial distribution data found", "WARNING")
            return

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        fig.suptitle('Spatial Distribution Analysis', fontsize=16, fontweight='bold')

        x_coords = [point[0] for point in spatial_data]
        y_coords = [point[1] for point in spatial_data]

        # 1. Scatter plot
        ax1.scatter(x_coords, y_coords, alpha=0.5, s=1)
        ax1.set_title('Target Center Positions')
        ax1.set_xlabel('X Coordinate')
        ax1.set_ylabel('Y Coordinate')
        ax1.grid(True, alpha=0.3)

        # 2. 2D Histogram (heatmap)
        ax2.hist2d(x_coords, y_coords, bins=50, cmap='hot')
        ax2.set_title('Spatial Density Heatmap')
        ax2.set_xlabel('X Coordinate')
        ax2.set_ylabel('Y Coordinate')

        plt.tight_layout()
        output_file = self.output_dir / "enhanced_spatial_distribution.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()

    def create_summary_report(self):
        """Create comprehensive summary report"""
        report = {
            'generation_time': datetime.now().isoformat(),
            'dataset_file': str(self.annotation_file),
            'total_stats': self.analysis_data['total_stats'],
            'batch_summary': {
                'total_batches': len(self.analysis_data['batch_stats']),
                'batch_details': {}
            },
            'size_distribution': dict(self.analysis_data['size_distribution'])
        }

        # Add batch details
        for batch_id, stats in self.analysis_data['batch_stats'].items():
            report['batch_summary']['batch_details'][batch_id] = {
                'image_count': stats['image_count'],
                'annotation_count': stats['annotation_count'],
                'avg_target_size': np.mean(stats['target_sizes']) if stats['target_sizes'] else 0,
                'target_density': stats['annotation_count'] / stats['image_count'] if stats['image_count'] > 0 else 0
            }

        # Save report
        report_file = self.output_dir / "enhanced_dataset_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)


def main():
    parser = argparse.ArgumentParser(description='Enhanced Dataset Visualizer with Progress Tracking')
    parser.add_argument('annotation_file', help='Path to COCO annotation file')
    parser.add_argument('-o', '--output', default='enhanced_visualization', help='Output directory')
    parser.add_argument('-v', '--verbose', action='store_true', help='Verbose output')
    parser.add_argument('--quiet', action='store_true', help='Quiet mode (minimal output)')

    args = parser.parse_args()

    try:
        # Create visualizer
        visualizer = EnhancedDatasetVisualizer(
            args.annotation_file,
            args.output,
            verbose=not args.quiet
        )

        # Generate visualizations
        visualizer.generate_enhanced_visualizations()

        print(f"\n🎉 Enhanced visualization completed successfully!")
        print(f"📁 Output directory: {args.output}")
        print(f"📊 Generated files:")
        print(f"  - enhanced_size_distribution.png")
        print(f"  - enhanced_batch_comparison.png")
        print(f"  - enhanced_spatial_distribution.png")
        print(f"  - enhanced_dataset_report.json")

    except Exception as e:
        print(f"❌ Error: {e}", file=sys.stderr)
        return 1

    return 0

if __name__ == "__main__":
    exit(main())
