# 数据采样策略设计

## 目标概述
从477,005个有效目标中筛选出30,000张高质量图像，确保数据分布合理且适合红外小目标检测训练。

## 当前数据分布分析

### 目标尺寸分布（原始）
| 尺寸范围 | 目标数量 | 占比 | 期望采样数量 |
|---------|---------|------|-------------|
| 0-4px   | 182,430 | 38.3% | 11,490 |
| 5-9px   | 159,225 | 33.4% | 10,020 |
| 10-14px | 63,366  | 13.3% | 3,990  |
| 15-19px | 30,291  | 6.3%  | 1,890  |
| 20-24px | 21,390  | 4.5%  | 1,350  |
| 25-29px | 13,821  | 2.9%  | 870   |
| 30-34px | 6,482   | 1.4%  | 420   |
| **总计** | **477,005** | **100%** | **30,000** |

## 采样策略设计

### 1. 尺寸分布优化策略
考虑到红外小目标检测的实际需求，我们需要调整原始分布：

#### 调整后的目标分布
| 尺寸范围 | 原始占比 | 调整后占比 | 目标采样数量 | 理由 |
|---------|---------|-----------|-------------|------|
| 0-4px   | 38.3%   | 25%       | 7,500      | 减少过小目标，提高检测精度 |
| 5-9px   | 33.4%   | 35%       | 10,500     | 核心尺寸范围，增加权重 |
| 10-14px | 13.3%   | 20%       | 6,000      | 重要尺寸，增加采样 |
| 15-19px | 6.3%    | 10%       | 3,000      | 中等尺寸，保持合理比例 |
| 20-24px | 4.5%    | 6%        | 1,800      | 较大目标，适当增加 |
| 25-29px | 2.9%    | 3%        | 900        | 大目标，保持比例 |
| 30-34px | 1.4%    | 1%        | 300        | 最大目标，少量采样 |

### 2. 时序采样策略
- **间隔采样**: 每5-10帧采样1帧，避免相邻帧相似度过高
- **随机偏移**: 在间隔基础上添加±2帧的随机偏移
- **batch内均匀**: 确保每个有效batch都有代表性采样

### 3. 质量筛选标准
#### 必须满足的条件
- ✅ 目标完全在图像边界内
- ✅ JSON文件格式正确，无解析错误
- ✅ 目标尺寸在3-33像素范围内
- ✅ 对应的PNG图像文件存在且可读

#### 优先级排序
1. **高优先级**: 目标清晰，bbox准确，无遮挡
2. **中优先级**: 目标稍有模糊但可识别
3. **低优先级**: 目标边缘或部分遮挡

### 4. 数据多样性保证
#### Batch级别多样性
- 确保50个有效batch都有采样
- 每个batch的采样数量与其数据质量成正比
- 避免某些batch过度采样

#### 场景多样性
- 不同时间段的数据
- 不同天气/光照条件
- 不同背景复杂度

## 实施计划

### 阶段1: 预处理和统计
1. 加载扫描结果 (`scan_results.json`)
2. 分析每个batch的数据质量和分布
3. 计算每个batch的采样配额

### 阶段2: 智能采样
1. 按尺寸分组进行分层采样
2. 在每个尺寸组内按batch均匀采样
3. 应用时序间隔过滤
4. 质量检查和验证

### 阶段3: 输出和验证
1. 生成COCO格式标注文件
2. 复制选中的图像文件
3. 生成采样报告和统计信息
4. 质量验证和可视化检查

## 预期输出

### 文件结构
```
output/
├── images/                 # 30,000张PNG图像
│   ├── batch1_001.png
│   ├── batch1_002.png
│   └── ...
├── annotations/
│   └── instances_train.json # COCO格式标注
├── reports/
│   ├── sampling_report.json # 采样统计报告
│   └── quality_report.json  # 质量分析报告
└── metadata/
    ├── batch_mapping.json   # batch映射关系
    └── size_distribution.json # 最终尺寸分布
```

### 质量指标
- **目标尺寸分布**: 符合调整后的目标分布
- **时序间隔**: 平均间隔7±2帧
- **数据质量**: 错误率 < 0.1%
- **Batch覆盖**: 所有50个有效batch都有采样
- **存储大小**: 约234GB (30,000 × 8MB)

## 配置参数

### 可调整参数
```python
SAMPLING_CONFIG = {
    "target_count": 30000,
    "frame_interval": (5, 10),
    "random_offset": 2,
    "size_distribution": {
        "0-4px": 0.25,
        "5-9px": 0.35,
        "10-14px": 0.20,
        "15-19px": 0.10,
        "20-24px": 0.06,
        "25-29px": 0.03,
        "30-34px": 0.01
    },
    "quality_threshold": 0.8,
    "max_errors_per_batch": 10
}
```

这个策略确保了数据的质量、多样性和分布合理性，为后续的模型训练提供高质量的数据集。
