# Sim2Real_30k 数据集网页云服务器部署指南

## 🌐 部署概述

本指南将帮助您将 Sim2Real_30k 数据集展示网页部署到云服务器上，实现在线访问。我们提供多种云服务器平台的部署方案。

## 🚀 快速部署方案

### 方案一：阿里云 ECS + Nginx（推荐）

#### 1. 购买和配置云服务器
```bash
# 推荐配置
- CPU: 1核心
- 内存: 2GB
- 存储: 40GB SSD
- 带宽: 1Mbps（按量付费）
- 操作系统: Ubuntu 20.04 LTS
```

#### 2. 连接服务器并安装环境
```bash
# SSH连接服务器
ssh root@your_server_ip

# 更新系统
apt update && apt upgrade -y

# 安装Nginx
apt install nginx -y

# 启动Nginx服务
systemctl start nginx
systemctl enable nginx

# 检查状态
systemctl status nginx
```

#### 3. 上传网页文件
```bash
# 在本地打包网页文件
cd K:\Sim2Real_30k\improved_website
tar -czf sim2real_website.tar.gz *

# 使用SCP上传到服务器
scp sim2real_website.tar.gz root@your_server_ip:/tmp/

# 在服务器上解压
cd /var/www/html
rm -rf *  # 清空默认文件
tar -xzf /tmp/sim2real_website.tar.gz -C /var/www/html/

# 设置权限
chown -R www-data:www-data /var/www/html/
chmod -R 755 /var/www/html/
```

#### 4. 配置Nginx
```bash
# 编辑Nginx配置
nano /etc/nginx/sites-available/default
```

```nginx
server {
    listen 80 default_server;
    listen [::]:80 default_server;

    root /var/www/html;
    index sim2real_30k_improved.html index.html;

    server_name your_domain.com;  # 替换为您的域名或IP

    location / {
        try_files $uri $uri/ =404;
    }

    # 启用gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # 设置缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

```bash
# 测试配置
nginx -t

# 重启Nginx
systemctl restart nginx
```

#### 5. 配置防火墙
```bash
# 开放HTTP端口
ufw allow 80
ufw allow 443  # 为HTTPS准备
ufw enable
```

### 方案二：腾讯云轻量应用服务器

#### 1. 购买轻量应用服务器
```bash
# 推荐配置
- 套餐: 1核2GB
- 系统: Ubuntu 20.04
- 流量包: 300GB/月
- 应用镜像: 选择"宝塔Linux面板"
```

#### 2. 使用宝塔面板部署
```bash
# 访问宝塔面板
http://your_server_ip:8888

# 安装LNMP环境
- Nginx 1.20+
- MySQL 5.7+ (可选)
- PHP 7.4+ (可选)

# 创建网站
1. 点击"网站" -> "添加站点"
2. 域名: your_domain.com
3. 根目录: /www/wwwroot/your_domain.com
4. 创建站点
```

#### 3. 上传文件
```bash
# 方法1: 使用宝塔面板文件管理器
1. 进入"文件"管理
2. 导航到 /www/wwwroot/your_domain.com
3. 上传 sim2real_website.tar.gz
4. 解压文件

# 方法2: 使用SCP
scp sim2real_website.tar.gz root@your_server_ip:/www/wwwroot/your_domain.com/
```

### 方案三：GitHub Pages（免费方案）

#### 1. 创建GitHub仓库
```bash
# 在GitHub上创建新仓库
仓库名: sim2real-30k-dataset
设置为Public
```

#### 2. 上传文件
```bash
# 克隆仓库到本地
git clone https://github.com/yourusername/sim2real-30k-dataset.git
cd sim2real-30k-dataset

# 复制网页文件
cp -r K:\Sim2Real_30k\improved_website\* .

# 重命名主页文件
mv sim2real_30k_improved.html index.html

# 提交文件
git add .
git commit -m "Add Sim2Real_30k dataset website"
git push origin main
```

#### 3. 启用GitHub Pages
```bash
1. 进入仓库设置 (Settings)
2. 滚动到 "Pages" 部分
3. Source: Deploy from a branch
4. Branch: main
5. Folder: / (root)
6. 点击 Save
```

#### 4. 访问网站
```bash
# 网站地址
https://yourusername.github.io/sim2real-30k-dataset/
```

## 🔧 高级配置

### SSL证书配置（HTTPS）

#### 使用Let's Encrypt免费证书
```bash
# 安装Certbot
apt install certbot python3-certbot-nginx -y

# 获取证书
certbot --nginx -d your_domain.com

# 自动续期
crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

### 域名配置

#### 1. 购买域名
- 推荐平台：阿里云、腾讯云、GoDaddy
- 建议选择 `.com` 或 `.cn` 域名

#### 2. DNS解析配置
```bash
# 添加A记录
类型: A
主机记录: @
记录值: your_server_ip
TTL: 600

# 添加CNAME记录（可选）
类型: CNAME
主机记录: www
记录值: your_domain.com
TTL: 600
```

### 性能优化

#### 1. 启用CDN加速
```bash
# 阿里云CDN配置
1. 开通CDN服务
2. 添加加速域名
3. 源站类型: IP
4. 源站地址: your_server_ip
5. 端口: 80
```

#### 2. 图片压缩优化
```bash
# 安装图片压缩工具
apt install imagemagick -y

# 批量压缩PNG图片
find /var/www/html -name "*.png" -exec convert {} -quality 85 {} \;
```

## 📊 监控和维护

### 1. 服务器监控
```bash
# 安装监控工具
apt install htop iotop -y

# 查看系统状态
htop
df -h
free -h
```

### 2. 日志管理
```bash
# Nginx访问日志
tail -f /var/log/nginx/access.log

# Nginx错误日志
tail -f /var/log/nginx/error.log

# 设置日志轮转
nano /etc/logrotate.d/nginx
```

### 3. 备份策略
```bash
# 创建备份脚本
nano /root/backup_website.sh
```

```bash
#!/bin/bash
# 网站备份脚本
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/root/backups"
WEBSITE_DIR="/var/www/html"

mkdir -p $BACKUP_DIR
tar -czf $BACKUP_DIR/website_backup_$DATE.tar.gz -C $WEBSITE_DIR .

# 保留最近7天的备份
find $BACKUP_DIR -name "website_backup_*.tar.gz" -mtime +7 -delete

echo "Backup completed: website_backup_$DATE.tar.gz"
```

```bash
# 设置定时备份
chmod +x /root/backup_website.sh
crontab -e
# 添加每日备份
0 2 * * * /root/backup_website.sh
```

## 🔍 故障排除

### 常见问题解决

#### 1. 网站无法访问
```bash
# 检查Nginx状态
systemctl status nginx

# 检查端口占用
netstat -tlnp | grep :80

# 检查防火墙
ufw status
```

#### 2. 图片显示异常
```bash
# 检查文件权限
ls -la /var/www/html/

# 修复权限
chown -R www-data:www-data /var/www/html/
chmod -R 755 /var/www/html/
```

#### 3. 页面加载缓慢
```bash
# 启用Nginx缓存
nano /etc/nginx/nginx.conf

# 在http块中添加
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=my_cache:10m max_size=10g inactive=60m use_temp_path=off;
```

## 💰 成本估算

### 各平台成本对比

| 平台 | 配置 | 月费用 | 年费用 | 备注 |
|------|------|--------|--------|------|
| 阿里云ECS | 1核2GB | ¥30-50 | ¥300-500 | 新用户有优惠 |
| 腾讯云轻量 | 1核2GB | ¥25-40 | ¥250-400 | 包含流量包 |
| GitHub Pages | - | 免费 | 免费 | 有流量限制 |
| Vercel | - | 免费 | 免费 | 适合静态网站 |

### 推荐方案
- **学术展示**: GitHub Pages（免费）
- **正式发布**: 阿里云/腾讯云（稳定可靠）
- **国际访问**: AWS/Google Cloud（全球加速）

## 📞 技术支持

如果在部署过程中遇到问题，可以：

1. **查看日志**: 检查Nginx和系统日志
2. **社区求助**: Stack Overflow、GitHub Issues
3. **云服务商支持**: 各平台都有技术支持服务
4. **文档参考**: 官方文档和教程

---

**部署完成后，您的数据集网站将可以通过域名或IP地址在全球范围内访问！** 🌍
