#!/usr/bin/env python3
"""
JSON文件诊断工具
用于分析和修复有问题的JSON标签文件
"""

import os
import json
import argparse
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Tuple

def analyze_json_file(json_path: Path) -> Dict:
    """分析单个JSON文件"""
    result = {
        'path': str(json_path),
        'size': 0,
        'status': 'unknown',
        'error': None,
        'content_preview': None
    }
    
    try:
        # 检查文件大小
        result['size'] = json_path.stat().st_size
        
        if result['size'] == 0:
            result['status'] = 'empty_file'
            return result
        
        # 读取文件内容
        with open(json_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if not content.strip():
            result['status'] = 'empty_content'
            return result
        
        # 保存内容预览
        result['content_preview'] = content[:200] + ('...' if len(content) > 200 else '')
        
        # 尝试解析JSON
        data = json.loads(content)
        
        if isinstance(data, list):
            result['status'] = 'valid_list'
            result['item_count'] = len(data)
        elif isinstance(data, dict):
            result['status'] = 'valid_dict'
        else:
            result['status'] = 'valid_other'
            result['data_type'] = type(data).__name__
        
    except json.JSONDecodeError as e:
        result['status'] = 'json_error'
        result['error'] = str(e)
    except Exception as e:
        result['status'] = 'other_error'
        result['error'] = str(e)
    
    return result

def find_problematic_json_files(root_path: Path, max_files: int = 50) -> List[Dict]:
    """查找有问题的JSON文件"""
    print(f"开始扫描JSON文件: {root_path}")
    
    problematic_files = []
    total_json_files = 0
    
    for json_file in root_path.rglob("*.json"):
        total_json_files += 1
        
        if total_json_files % 100 == 0:
            print(f"已检查 {total_json_files} 个JSON文件...")
        
        analysis = analyze_json_file(json_file)
        
        # 只收集有问题的文件
        if analysis['status'] in ['empty_file', 'empty_content', 'json_error', 'other_error']:
            problematic_files.append(analysis)
            
            if len(problematic_files) >= max_files:
                print(f"已找到 {max_files} 个问题文件，停止扫描")
                break
    
    print(f"扫描完成: 总共 {total_json_files} 个JSON文件，发现 {len(problematic_files)} 个问题文件")
    return problematic_files

def print_diagnostic_report(problematic_files: List[Dict]):
    """打印诊断报告"""
    print("\n" + "="*80)
    print("JSON文件诊断报告")
    print("="*80)
    
    # 按状态分组统计
    status_count = defaultdict(int)
    for file_info in problematic_files:
        status_count[file_info['status']] += 1
    
    print(f"问题文件统计:")
    for status, count in sorted(status_count.items()):
        print(f"  {status}: {count} 个文件")
    
    print(f"\n问题文件详情 (显示前10个):")
    print("-" * 80)
    
    for i, file_info in enumerate(problematic_files[:10]):
        print(f"\n{i+1}. {Path(file_info['path']).name}")
        print(f"   路径: {file_info['path']}")
        print(f"   大小: {file_info['size']} 字节")
        print(f"   状态: {file_info['status']}")
        
        if file_info['error']:
            print(f"   错误: {file_info['error']}")
        
        if file_info['content_preview']:
            print(f"   内容预览: {repr(file_info['content_preview'])}")

def suggest_solutions(problematic_files: List[Dict]):
    """提供解决方案建议"""
    print(f"\n" + "="*80)
    print("解决方案建议")
    print("="*80)
    
    status_count = defaultdict(int)
    for file_info in problematic_files:
        status_count[file_info['status']] += 1
    
    if status_count['empty_file'] > 0:
        print(f"📁 空文件 ({status_count['empty_file']} 个):")
        print(f"   - 这些文件完全为空，可能是生成过程中的错误")
        print(f"   - 建议: 删除这些文件或重新生成对应的标签")
    
    if status_count['empty_content'] > 0:
        print(f"📄 空内容 ({status_count['empty_content']} 个):")
        print(f"   - 文件存在但内容为空或只有空白字符")
        print(f"   - 建议: 检查数据生成流程")
    
    if status_count['json_error'] > 0:
        print(f"🔧 JSON格式错误 ({status_count['json_error']} 个):")
        print(f"   - JSON语法错误，无法解析")
        print(f"   - 建议: 检查文件编码和JSON格式")
        print(f"   - 可以尝试手动修复或重新生成")

def main():
    parser = argparse.ArgumentParser(description="JSON文件诊断工具")
    parser.add_argument('path', help='要诊断的根路径')
    parser.add_argument('-m', '--max-files', type=int, default=50, 
                       help='最大检查的问题文件数量 (默认: 50)')
    parser.add_argument('-o', '--output', help='输出详细报告到文件')
    
    args = parser.parse_args()
    
    root_path = Path(args.path)
    if not root_path.exists():
        print(f"❌ 路径不存在: {root_path}")
        return 1
    
    # 查找问题文件
    problematic_files = find_problematic_json_files(root_path, args.max_files)
    
    # 打印报告
    print_diagnostic_report(problematic_files)
    suggest_solutions(problematic_files)
    
    # 保存详细报告
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(problematic_files, f, ensure_ascii=False, indent=2)
        print(f"\n✓ 详细报告已保存到: {args.output}")
    
    return 0

if __name__ == "__main__":
    exit(main())
