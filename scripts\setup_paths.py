#!/usr/bin/env python3
"""
路径配置助手
帮助用户快速配置数据集路径，支持跨平台部署
"""

import json
import os
import sys
from pathlib import Path
import argparse

def detect_platform():
    """检测操作系统平台"""
    if os.name == 'nt':
        return 'windows'
    elif sys.platform == 'darwin':
        return 'mac'
    else:
        return 'linux'

def get_default_paths(platform):
    """获取平台默认路径建议"""
    defaults = {
        'windows': {
            'dataset_root': 'F:\\Sim2Real_30k',
            'output_root': 'F:\\Sim2Real_30k_output',
            'temp_dir': 'F:\\temp\\sim2real'
        },
        'linux': {
            'dataset_root': '/mnt/data/Sim2Real_30k',
            'output_root': '/mnt/output/Sim2Real_30k',
            'temp_dir': '/tmp/sim2real'
        },
        'mac': {
            'dataset_root': '/Volumes/Data/Sim2Real_30k',
            'output_root': f'{Path.home()}/Documents/Sim2Real_30k_output',
            'temp_dir': '/tmp/sim2real'
        }
    }
    return defaults.get(platform, defaults['linux'])

def validate_path(path_str, path_type="directory"):
    """验证路径有效性"""
    try:
        path = Path(path_str)
        
        if path_type == "directory":
            if path.exists() and not path.is_dir():
                return False, f"路径存在但不是目录: {path}"
            elif not path.exists():
                return True, f"目录不存在，将在需要时创建: {path}"
            else:
                return True, f"目录存在且有效: {path}"
        
        return True, "路径格式有效"
    
    except Exception as e:
        return False, f"路径格式无效: {e}"

def interactive_setup():
    """交互式路径配置"""
    print("=" * 60)
    print("Sim2Real_30k 数据集工具路径配置助手")
    print("=" * 60)
    
    platform = detect_platform()
    print(f"检测到操作系统: {platform}")
    
    defaults = get_default_paths(platform)
    print(f"\n推荐的默认路径配置:")
    for key, value in defaults.items():
        print(f"  {key}: {value}")
    
    print(f"\n请配置您的路径 (直接回车使用默认值):")
    
    # 数据集根目录
    while True:
        dataset_root = input(f"数据集根目录 [{defaults['dataset_root']}]: ").strip().strip('"\'')
        if not dataset_root:
            dataset_root = defaults['dataset_root']
        
        valid, msg = validate_path(dataset_root)
        print(f"  {msg}")
        
        if valid:
            break
        else:
            print("  请重新输入有效路径")
    
    # 输出目录
    while True:
        output_root = input(f"输出目录 [{defaults['output_root']}]: ").strip().strip('"\'')
        if not output_root:
            output_root = defaults['output_root']
        
        valid, msg = validate_path(output_root)
        print(f"  {msg}")
        
        if valid:
            break
        else:
            print("  请重新输入有效路径")
    
    # 临时目录
    while True:
        temp_dir = input(f"临时目录 [{defaults['temp_dir']}]: ").strip().strip('"\'')
        if not temp_dir:
            temp_dir = defaults['temp_dir']
        
        valid, msg = validate_path(temp_dir)
        print(f"  {msg}")
        
        if valid:
            break
        else:
            print("  请重新输入有效路径")
    
    return {
        'dataset_root': dataset_root,
        'output_root': output_root,
        'temp_dir': temp_dir,
        'platform': platform
    }

def create_config(paths_config, config_dir="config"):
    """创建配置文件"""
    config_path = Path(config_dir)
    config_path.mkdir(exist_ok=True)
    
    # 完整配置
    full_config = {
        "dataset_root": paths_config['dataset_root'],
        "output_root": paths_config['output_root'],
        "temp_dir": paths_config['temp_dir'],
        "platform": paths_config['platform'],
        "created_by": "setup_paths.py",
        "version": "1.0",
        "subdirectories": {
            "images": "images",
            "annotations": "annotations",
            "reports": "reports",
            "visualization": "visualization",
            "domain_analysis": "domain_analysis"
        },
        "file_patterns": {
            "scan_results": "scan_results.json",
            "complete_annotations": "instances_complete.json",
            "train_annotations": "instances_train_split.json",
            "val_annotations": "instances_val_split.json"
        }
    }
    
    # 保存路径配置
    paths_file = config_path / "paths_config.json"
    with open(paths_file, 'w', encoding='utf-8') as f:
        json.dump(full_config, f, ensure_ascii=False, indent=2)
    
    print(f"\n✓ 路径配置已保存到: {paths_file}")
    
    return paths_file

def create_directories(paths_config):
    """创建必要的目录"""
    dirs_to_create = [
        paths_config['output_root'],
        paths_config['temp_dir'],
        str(Path(paths_config['output_root']) / 'images'),
        str(Path(paths_config['output_root']) / 'annotations'),
        str(Path(paths_config['output_root']) / 'reports'),
        str(Path(paths_config['output_root']) / 'visualization'),
        str(Path(paths_config['output_root']) / 'domain_analysis')
    ]
    
    created_dirs = []
    for dir_path in dirs_to_create:
        path = Path(dir_path)
        if not path.exists():
            try:
                path.mkdir(parents=True, exist_ok=True)
                created_dirs.append(str(path))
            except Exception as e:
                print(f"⚠ 无法创建目录 {path}: {e}")
    
    if created_dirs:
        print(f"\n✓ 已创建目录:")
        for dir_path in created_dirs:
            print(f"  - {dir_path}")

def generate_usage_examples(paths_config):
    """生成使用示例"""
    dataset_root = paths_config['dataset_root']
    output_root = paths_config['output_root']
    
    examples = f"""
# 使用示例命令 (基于您的配置)

# 1. 数据扫描
python scripts/dataset_scanner.py -p "{dataset_root}" -o scan_results.json

# 2. 数据处理
python scripts/data_processor.py scan_results.json -o "{output_root}" -n 30000

# 3. 数据集划分
python scripts/dataset_splitter.py "{output_root}/annotations/instances_complete.json" -o "{output_root}" --train-ratio 0.7

# 4. 可视化分析
python scripts/dataset_visualizer_en.py "{output_root}/annotations/instances_complete.json" -o "{output_root}/visualization"

# 5. 域差距分析
python scripts/domain_gap_analyzer.py "{output_root}/annotations/instances_complete.json" "{output_root}/images" -o "{output_root}/domain_analysis"
"""
    
    # 保存示例到文件
    examples_file = Path("usage_examples.txt")
    with open(examples_file, 'w', encoding='utf-8') as f:
        f.write(examples)
    
    print(f"\n✓ 使用示例已保存到: {examples_file}")
    print("您可以直接复制这些命令来运行工具")

def load_existing_config(config_file="config/paths_config.json"):
    """加载现有配置"""
    config_path = Path(config_file)
    if config_path.exists():
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠ 无法加载现有配置: {e}")
    return None

def main():
    parser = argparse.ArgumentParser(description="Sim2Real_30k 路径配置助手")
    parser.add_argument('--config-dir', default='config', help='配置文件目录')
    parser.add_argument('--auto', action='store_true', help='使用默认配置（非交互模式）')
    parser.add_argument('--show-existing', action='store_true', help='显示现有配置')
    
    args = parser.parse_args()
    
    # 显示现有配置
    if args.show_existing:
        existing = load_existing_config()
        if existing:
            print("现有配置:")
            print(json.dumps(existing, ensure_ascii=False, indent=2))
        else:
            print("未找到现有配置")
        return
    
    # 自动模式
    if args.auto:
        platform = detect_platform()
        paths_config = get_default_paths(platform)
        paths_config['platform'] = platform
        print(f"使用默认配置 ({platform}):")
        for key, value in paths_config.items():
            print(f"  {key}: {value}")
    else:
        # 交互模式
        paths_config = interactive_setup()
    
    # 创建配置文件
    config_file = create_config(paths_config, args.config_dir)
    
    # 询问是否创建目录
    create_dirs = input(f"\n是否创建必要的目录? (y/n) [y]: ").strip().lower()
    if create_dirs in ['', 'y', 'yes']:
        create_directories(paths_config)
    
    # 生成使用示例
    generate_examples = input(f"是否生成使用示例? (y/n) [y]: ").strip().lower()
    if generate_examples in ['', 'y', 'yes']:
        generate_usage_examples(paths_config)
    
    print(f"\n" + "=" * 60)
    print("配置完成！")
    print("=" * 60)
    print(f"配置文件: {config_file}")
    print(f"数据集路径: {paths_config['dataset_root']}")
    print(f"输出路径: {paths_config['output_root']}")
    print(f"临时路径: {paths_config['temp_dir']}")
    print("\n现在您可以开始使用数据集处理工具了！")

if __name__ == "__main__":
    main()
