#!/usr/bin/env python3
"""
Dataset Visualization Tool (English Version)
Generate comprehensive dataset introduction and analysis reports
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Tuple, Optional
import argparse

# Set font and style
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
sns.set_palette("husl")

class DatasetVisualizerEN:
    def __init__(self, annotation_file: str, output_dir: str):
        self.annotation_file = annotation_file
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Load data
        self.coco_data = self.load_coco_data()
        self.analysis_data = self.analyze_dataset()
        
    def load_coco_data(self) -> Dict:
        """Load COCO format data"""
        print(f"Loading dataset: {self.annotation_file}")
        with open(self.annotation_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"✓ Dataset loaded:")
        print(f"  - Images: {len(data['images'])}")
        print(f"  - Annotations: {len(data['annotations'])}")
        print(f"  - Categories: {len(data['categories'])}")
        
        return data
    
    def analyze_dataset(self) -> Dict:
        """Analyze dataset statistics"""
        print("Analyzing dataset statistics...")
        
        # Extract batch information
        batch_stats = defaultdict(lambda: {
            'image_count': 0,
            'annotation_count': 0,
            'target_sizes': [],
            'target_areas': [],
            'targets_per_image': []
        })
        
        # Analyze images and batch distribution
        for image in self.coco_data['images']:
            filename = image['file_name']
            # Extract batch information (assuming format: batch{N}_{id}.png)
            if filename.startswith('batch'):
                batch_id = filename.split('_')[0]
                batch_stats[batch_id]['image_count'] += 1
        
        # Analyze annotation information
        image_annotations = defaultdict(list)
        for ann in self.coco_data['annotations']:
            image_annotations[ann['image_id']].append(ann)
        
        # Calculate detailed statistics for each batch
        for image in self.coco_data['images']:
            filename = image['file_name']
            if filename.startswith('batch'):
                batch_id = filename.split('_')[0]
                image_id = image['id']
                
                # Annotations for this image
                annotations = image_annotations.get(image_id, [])
                batch_stats[batch_id]['annotation_count'] += len(annotations)
                batch_stats[batch_id]['targets_per_image'].append(len(annotations))
                
                # Analyze target sizes
                for ann in annotations:
                    bbox = ann['bbox']
                    width, height = bbox[2], bbox[3]
                    size = max(width, height)
                    area = ann['area']
                    
                    batch_stats[batch_id]['target_sizes'].append(size)
                    batch_stats[batch_id]['target_areas'].append(area)
        
        # Global statistics
        all_sizes = []
        all_areas = []
        all_targets_per_image = []
        
        for batch_data in batch_stats.values():
            all_sizes.extend(batch_data['target_sizes'])
            all_areas.extend(batch_data['target_areas'])
            all_targets_per_image.extend(batch_data['targets_per_image'])
        
        analysis = {
            'batch_stats': dict(batch_stats),
            'global_stats': {
                'total_batches': len(batch_stats),
                'total_images': len(self.coco_data['images']),
                'total_annotations': len(self.coco_data['annotations']),
                'avg_targets_per_image': np.mean(all_targets_per_image) if all_targets_per_image else 0,
                'target_sizes': all_sizes,
                'target_areas': all_areas,
                'size_stats': {
                    'min': min(all_sizes) if all_sizes else 0,
                    'max': max(all_sizes) if all_sizes else 0,
                    'mean': np.mean(all_sizes) if all_sizes else 0,
                    'std': np.std(all_sizes) if all_sizes else 0,
                    'median': np.median(all_sizes) if all_sizes else 0
                }
            }
        }
        
        print(f"✓ Analysis completed:")
        print(f"  - Total batches: {analysis['global_stats']['total_batches']}")
        print(f"  - Avg targets per image: {analysis['global_stats']['avg_targets_per_image']:.2f}")
        print(f"  - Target size range: {analysis['global_stats']['size_stats']['min']:.1f} - {analysis['global_stats']['size_stats']['max']:.1f} pixels")
        
        return analysis
    
    def plot_size_distribution(self):
        """Plot target size distribution"""
        print("Generating target size distribution plots...")
        
        sizes = self.analysis_data['global_stats']['target_sizes']
        if not sizes:
            print("⚠ No target size data")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Infrared Small Target Size Distribution Analysis', fontsize=16, fontweight='bold')
        
        # 1. Size histogram
        axes[0, 0].hist(sizes, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_xlabel('Target Size (pixels)')
        axes[0, 0].set_ylabel('Frequency')
        axes[0, 0].set_title('Target Size Histogram')
        axes[0, 0].grid(True, alpha=0.3)
        
        # Add statistical information
        stats = self.analysis_data['global_stats']['size_stats']
        axes[0, 0].axvline(stats['mean'], color='red', linestyle='--', 
                          label=f'Mean: {stats["mean"]:.1f}')
        axes[0, 0].axvline(stats['median'], color='green', linestyle='--', 
                          label=f'Median: {stats["median"]:.1f}')
        axes[0, 0].legend()
        
        # 2. Size range distribution
        size_ranges = {
            'Tiny Targets (3-8px)': (3, 8),
            'Small Targets (8-16px)': (8, 16),
            'Medium Targets (16-24px)': (16, 24),
            'Large Targets (24-33px)': (24, 33)
        }
        
        range_counts = {}
        for range_name, (min_size, max_size) in size_ranges.items():
            count = sum(1 for s in sizes if min_size <= s < max_size)
            range_counts[range_name] = count
        
        # If there are targets out of range
        out_of_range = sum(1 for s in sizes if s >= 33)
        if out_of_range > 0:
            range_counts['Extra Large (≥33px)'] = out_of_range
        
        # Pie chart
        labels = list(range_counts.keys())
        values = list(range_counts.values())
        colors = plt.cm.Set3(np.linspace(0, 1, len(labels)))
        
        wedges, texts, autotexts = axes[0, 1].pie(values, labels=labels, autopct='%1.1f%%',
                                                 colors=colors, startangle=90)
        axes[0, 1].set_title('Target Size Range Distribution')
        
        # 3. Cumulative distribution function
        sorted_sizes = np.sort(sizes)
        y = np.arange(1, len(sorted_sizes) + 1) / len(sorted_sizes)
        axes[1, 0].plot(sorted_sizes, y, linewidth=2, color='purple')
        axes[1, 0].set_xlabel('Target Size (pixels)')
        axes[1, 0].set_ylabel('Cumulative Probability')
        axes[1, 0].set_title('Target Size Cumulative Distribution Function')
        axes[1, 0].grid(True, alpha=0.3)
        
        # Add key percentiles
        percentiles = [25, 50, 75, 90, 95]
        for p in percentiles:
            value = np.percentile(sizes, p)
            axes[1, 0].axvline(value, color='red', alpha=0.5, linestyle=':')
            axes[1, 0].text(value, p/100, f'P{p}: {value:.1f}', 
                           rotation=90, verticalalignment='bottom')
        
        # 4. Box plot
        axes[1, 1].boxplot(sizes, vert=True, patch_artist=True,
                          boxprops=dict(facecolor='lightblue', alpha=0.7))
        axes[1, 1].set_ylabel('Target Size (pixels)')
        axes[1, 1].set_title('Target Size Box Plot')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'size_distribution_en.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✓ Size distribution plot saved: {self.output_dir / 'size_distribution_en.png'}")
    
    def plot_batch_comparison(self):
        """Plot batch comparison analysis"""
        print("Generating batch comparison analysis...")
        
        batch_stats = self.analysis_data['batch_stats']
        if not batch_stats:
            print("⚠ No batch data")
            return
        
        # Prepare data
        batch_names = sorted(batch_stats.keys(), key=lambda x: int(x.replace('batch', '')))
        image_counts = [batch_stats[b]['image_count'] for b in batch_names]
        annotation_counts = [batch_stats[b]['annotation_count'] for b in batch_names]
        avg_targets = [np.mean(batch_stats[b]['targets_per_image']) if batch_stats[b]['targets_per_image'] else 0 
                      for b in batch_names]
        avg_sizes = [np.mean(batch_stats[b]['target_sizes']) if batch_stats[b]['target_sizes'] else 0 
                    for b in batch_names]
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Inter-Batch Data Distribution Comparison', fontsize=16, fontweight='bold')
        
        # 1. Number of images per batch
        bars1 = axes[0, 0].bar(range(len(batch_names)), image_counts, 
                              color='lightcoral', alpha=0.7)
        axes[0, 0].set_xlabel('Batch ID')
        axes[0, 0].set_ylabel('Number of Images')
        axes[0, 0].set_title('Image Count Distribution Across Batches')
        axes[0, 0].set_xticks(range(0, len(batch_names), max(1, len(batch_names)//10)))
        axes[0, 0].set_xticklabels([batch_names[i] for i in range(0, len(batch_names), max(1, len(batch_names)//10))], 
                                  rotation=45)
        
        # 2. Number of annotations per batch
        bars2 = axes[0, 1].bar(range(len(batch_names)), annotation_counts, 
                              color='lightgreen', alpha=0.7)
        axes[0, 1].set_xlabel('Batch ID')
        axes[0, 1].set_ylabel('Number of Annotations')
        axes[0, 1].set_title('Annotation Count Distribution Across Batches')
        axes[0, 1].set_xticks(range(0, len(batch_names), max(1, len(batch_names)//10)))
        axes[0, 1].set_xticklabels([batch_names[i] for i in range(0, len(batch_names), max(1, len(batch_names)//10))], 
                                  rotation=45)
        
        # 3. Average targets per image
        axes[1, 0].plot(range(len(batch_names)), avg_targets, 'o-', 
                       color='blue', linewidth=2, markersize=4)
        axes[1, 0].set_xlabel('Batch ID')
        axes[1, 0].set_ylabel('Average Targets per Image')
        axes[1, 0].set_title('Average Targets per Image Across Batches')
        axes[1, 0].set_xticks(range(0, len(batch_names), max(1, len(batch_names)//10)))
        axes[1, 0].set_xticklabels([batch_names[i] for i in range(0, len(batch_names), max(1, len(batch_names)//10))], 
                                  rotation=45)
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. Average target size
        axes[1, 1].plot(range(len(batch_names)), avg_sizes, 's-', 
                       color='purple', linewidth=2, markersize=4)
        axes[1, 1].set_xlabel('Batch ID')
        axes[1, 1].set_ylabel('Average Target Size (pixels)')
        axes[1, 1].set_title('Average Target Size Across Batches')
        axes[1, 1].set_xticks(range(0, len(batch_names), max(1, len(batch_names)//10)))
        axes[1, 1].set_xticklabels([batch_names[i] for i in range(0, len(batch_names), max(1, len(batch_names)//10))], 
                                  rotation=45)
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'batch_comparison_en.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✓ Batch comparison plot saved: {self.output_dir / 'batch_comparison_en.png'}")
    
    def generate_all_visualizations(self):
        """Generate all visualization charts"""
        print("Starting complete dataset visualization report generation...")
        print("="*60)
        
        # Generate various visualizations
        self.plot_size_distribution()
        self.plot_batch_comparison()
        
        print("="*60)
        print("✓ All visualization charts generated!")
        print(f"Output directory: {self.output_dir}")
        print("Generated files:")
        print("  - size_distribution_en.png    # Target size distribution analysis")
        print("  - batch_comparison_en.png     # Inter-batch comparison analysis")
        print("="*60)

def main():
    parser = argparse.ArgumentParser(description="Infrared Small Target Detection Dataset Visualization Tool (English)")
    parser.add_argument('annotation_file', help='COCO format annotation file path')
    parser.add_argument('-o', '--output', default='visualization_output_en', help='Output directory')
    
    args = parser.parse_args()
    
    # Validate input file
    if not Path(args.annotation_file).exists():
        print(f"❌ Annotation file not found: {args.annotation_file}")
        return 1
    
    # Create visualizer and generate report
    visualizer = DatasetVisualizerEN(args.annotation_file, args.output)
    visualizer.generate_all_visualizations()
    
    return 0

if __name__ == "__main__":
    exit(main())
