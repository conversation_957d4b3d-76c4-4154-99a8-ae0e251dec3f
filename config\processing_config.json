{"target_count": 30000, "frame_interval": [5, 10], "random_offset": 2, "size_distribution": {"0-4px": 0.25, "5-9px": 0.35, "10-14px": 0.2, "15-19px": 0.1, "20-24px": 0.06, "25-29px": 0.03, "30-34px": 0.01}, "quality_threshold": 0.8, "max_errors_per_batch": 10, "image_size": {"width": 1920, "height": 1080}, "output_format": {"image_format": "png", "annotation_format": "coco", "naming_pattern": "batch{batch_id}_{image_id:06d}.png"}, "processing_options": {"copy_images": true, "generate_reports": true, "validate_outputs": true, "create_visualization": false}}