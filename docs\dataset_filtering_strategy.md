# 红外小目标检测数据集筛选策略文档

## 项目概述
- **任务目标**: 从100万张游戏红外图像中筛选3-5万张高质量数据用于红外小目标检测
- **数据来源**: 多个移动硬盘中的游戏导出数据集
- **当前处理**: `G:\孤立的文件\孤立的文件 45`
- **输出存储**: D盘(200G) + E盘(300G)

## 数据集结构
### 文件配对模式
1. **模式1**: `batch5_22080717007_1.png` + `batch5_22080717007_1.json`
2. **模式2**: `output_images1.png` + `video_label1.json`

### JSON标签格式
```json
{
    "bbox": [x1, y1, x2, y2],
    "id": 1,
    "isdead": false,
    "isvisible": true,
    "m_blk_path": "gameData/flightModels/bf-109b_2.blk",
    "m_vehicle_type": "exp_fighter"
}
```

## 筛选标准

### 1. 目标类型筛选
- **必须包含**: `m_blk_path`中包含`"gameData/flightModels/"`
- **状态要求**: `isdead: false` 且 `isvisible: true`
- **目标类别**: 所有飞机统一为一个类别

### 2. 目标尺寸要求
- **尺寸范围**: 3-33像素
- **计算方式**: `max(bbox_width, bbox_height)`
- **排除**: 1-2像素的过小目标

### 3. 时序采样策略
- **跳帧间隔**: 每5-10帧采样1帧
- **目的**: 避免相邻帧相似度过高影响训练公平性

### 4. 质量要求
- **完整性**: bbox不超出图像边界
- **清晰度**: 目标区域不全黑或全白
- **背景**: 不做严格要求(红外天空背景本身单一)

## 命名规范

### Batch编号
- 每个包含数据的子文件夹 = 一个batch
- 按处理顺序编号: batch1, batch2, batch3...

### 文件命名
- **图像**: `batch{N}_{序号}.png`
- **标签**: `batch{N}_{序号}.json`
- **示例**: `batch1_001.png` + `batch1_001.json`

## 输出格式
- **图像格式**: PNG (保持原格式)
- **标签格式**: COCO JSON格式
- **类别定义**: 单一类别 "aircraft", category_id=1

## 处理流程
1. 递归扫描所有子文件夹
2. 识别图像-标签配对
3. 解析JSON并应用筛选标准
4. 按时序间隔采样
5. 重命名并转换为COCO格式
6. 统计最终数据量

## 预期结果
- 筛选出符合标准的所有数据(数量不限)
- 后续从中选择3-5万张用于训练
- 保持batch独立性便于后续数据集划分