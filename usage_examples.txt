
# 使用示例命令 (基于您的配置)

# 1. 数据扫描
python scripts/dataset_scanner.py -p "F:\Sim2Real_30k" -o scan_results.json

# 2. 数据处理
python scripts/data_processor.py scan_results.json -o "F:\Sim2Real_30k_output" -n 30000

# 3. 数据集划分
python scripts/dataset_splitter.py "F:\Sim2Real_30k_output/annotations/instances_complete.json" -o "F:\Sim2Real_30k_output" --train-ratio 0.7

# 4. 可视化分析
python scripts/dataset_visualizer_en.py "F:\Sim2Real_30k_output/annotations/instances_complete.json" -o "F:\Sim2Real_30k_output/visualization"

# 5. 域差距分析
python scripts/domain_gap_analyzer.py "F:\Sim2Real_30k_output/annotations/instances_complete.json" "F:\Sim2Real_30k_output/images" -o "F:\Sim2Real_30k_output/domain_analysis"
