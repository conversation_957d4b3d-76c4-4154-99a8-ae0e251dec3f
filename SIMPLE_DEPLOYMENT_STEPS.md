# 🚀 Sim2Real_30k 网页部署到 ************** - 简化步骤

## 📋 部署前准备（在本地完成）

### 1. 打包网页文件
在本地Windows系统中：
```
1. 进入目录：K:\Sim2Real_30k\final_with_batch_analysis\
2. 选择所有文件（Ctrl+A）
3. 右键 → 添加到压缩文件 → 命名为 sim2real_website.zip
```

### 2. 上传到网盘
推荐使用百度网盘：
```
1. 登录百度网盘
2. 上传 sim2real_website.zip
3. 右键文件 → 分享 → 创建链接
4. 复制分享链接（记住这个链接）
```

## 🌐 服务器部署（通过网页终端）

### 步骤1：登录服务器
1. 打开云服务商控制台
2. 找到服务器 **************
3. 点击"远程连接"或"网页终端"
4. 输入用户名密码登录

### 步骤2：安装Web服务器
在终端中逐行执行：

```bash
# 更新系统
sudo apt update

# 安装Nginx
sudo apt install nginx -y

# 启动服务
sudo systemctl start nginx
sudo systemctl enable nginx

# 开放端口
sudo ufw allow 80
```

### 步骤3：下载并部署网页
```bash
# 进入网页目录
cd /var/www/html/

# 清空默认文件
sudo rm -rf *

# 下载文件（替换为您的百度网盘链接）
sudo wget "您的百度网盘直链" -O sim2real_website.zip

# 解压文件
sudo unzip sim2real_website.zip

# 设置权限
sudo chown -R www-data:www-data /var/www/html/
sudo chmod -R 755 /var/www/html/

# 创建默认页面
sudo ln -sf sim2real_30k_improved.html index.html
```

### 步骤4：重启服务
```bash
sudo systemctl restart nginx
```

## 🎯 访问测试

在浏览器中访问：
- **主页面**: http://**************/sim2real_30k_improved.html
- **默认页**: http://**************/

## 🔧 如果百度网盘直链不可用

### 方案A：使用GitHub
```bash
# 1. 先在GitHub创建仓库并上传文件
# 2. 在服务器执行：
cd /var/www/html/
sudo rm -rf *
sudo git clone https://github.com/yourusername/sim2real-30k-dataset.git .
sudo chown -R www-data:www-data /var/www/html/
sudo chmod -R 755 /var/www/html/
```

### 方案B：使用WeTransfer
```bash
# 1. 上传到 wetransfer.com 获取下载链接
# 2. 在服务器执行：
cd /var/www/html/
sudo rm -rf *
sudo wget "WeTransfer下载链接" -O sim2real_website.zip
sudo unzip sim2real_website.zip
sudo chown -R www-data:www-data /var/www/html/
sudo chmod -R 755 /var/www/html/
```

## 🛠️ 常见问题解决

### 问题1：wget下载失败
```bash
# 尝试使用curl
sudo curl -L "下载链接" -o sim2real_website.zip
```

### 问题2：权限问题
```bash
sudo chown -R www-data:www-data /var/www/html/
sudo chmod -R 755 /var/www/html/
```

### 问题3：Nginx无法启动
```bash
sudo systemctl status nginx
sudo nginx -t
sudo systemctl restart nginx
```

### 问题4：无法访问网页
```bash
# 检查防火墙
sudo ufw status
sudo ufw allow 80

# 检查Nginx状态
sudo systemctl status nginx
```

## 📱 一键部署脚本

如果您想使用自动化脚本，可以：

```bash
# 下载部署脚本
wget https://raw.githubusercontent.com/yourusername/scripts/main/quick_deploy_script.sh

# 给脚本执行权限
chmod +x quick_deploy_script.sh

# 运行脚本
sudo ./quick_deploy_script.sh
```

## 🎉 部署完成检查清单

- [ ] Nginx服务正常运行
- [ ] 网页文件已上传到 /var/www/html/
- [ ] 文件权限设置正确
- [ ] 防火墙已开放80端口
- [ ] 可以通过浏览器访问网页

## 📞 需要帮助？

如果遇到问题，请提供：
1. 错误信息截图
2. 执行的命令
3. 服务器系统版本：`cat /etc/os-release`
4. Nginx状态：`sudo systemctl status nginx`

---

**预计部署时间**: 10-15分钟  
**访问地址**: http://**************/sim2real_30k_improved.html  
**推荐方案**: 百度网盘 + 网页终端
