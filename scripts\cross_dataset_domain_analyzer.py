#!/usr/bin/env python3
"""
Cross-Dataset Domain Gap Analysis Tool
Analyze domain differences between Sim2Real_30k and other infrared small target datasets
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from collections import defaultdict
from typing import Dict, List, Tuple, Optional
import argparse
import time
from datetime import datetime
import cv2

# Try to import required libraries
try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    from torchvision import transforms
    from PIL import Image
    TORCH_AVAILABLE = True
except ImportError:
    print("⚠️ PyTorch not available, using traditional feature extraction")
    TORCH_AVAILABLE = False

try:
    from sklearn.decomposition import PCA
    from sklearn.cluster import KMeans
    from sklearn.manifold import TSNE
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    print("⚠️ scikit-learn not available, some features will be disabled")
    SKLEARN_AVAILABLE = False

try:
    from tqdm import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False

# Set style
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

class SimpleFeatureExtractor(nn.Module):
    """Simplified CNN for feature extraction"""
    def __init__(self):
        super(SimpleFeatureExtractor, self).__init__()
        self.conv1 = nn.Conv2d(1, 32, 3, padding=1)
        self.conv2 = nn.Conv2d(32, 64, 3, padding=1)
        self.conv3 = nn.Conv2d(64, 128, 3, padding=1)
        self.pool = nn.AdaptiveAvgPool2d((1, 1))
        
    def forward(self, x):
        x = F.relu(self.conv1(x))
        x = F.max_pool2d(x, 2)
        x = F.relu(self.conv2(x))
        x = F.max_pool2d(x, 2)
        x = F.relu(self.conv3(x))
        x = self.pool(x)
        return x.view(x.size(0), -1)

class CrossDatasetDomainAnalyzer:
    def __init__(self, output_dir: str, max_images_per_dataset: int = 100):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.max_images_per_dataset = max_images_per_dataset
        
        # Dataset configurations
        self.dataset_configs = {
            'Sim2Real_30k': {
                'path': 'K:/Sim2Real_30k/images',
                'description': 'Sim2Real 30k infrared small target dataset',
                'type': 'processed'
            },
            'DenseSIRST': {
                'path': 'F:/dataset/Infrared/DenseSIRST/DenseSIRST/SIRSTdevkit/PNGImages',
                'description': 'Dense clustered infrared small target dataset',
                'type': 'direct'
            },
            'IRSatVideo-LEO': {
                'path': 'F:/dataset/Infrared/IRSatVideo-LEO/images',
                'description': 'Infrared satellite video dataset',
                'type': 'nested'
            },
            'IRSTD-1k': {
                'path': 'F:/dataset/Infrared/IRSTD-1k/IRSTD-1k/images',
                'description': 'IRSTD-1k realistic infrared small target dataset',
                'type': 'direct'
            },
            'NoisySIRST': {
                'path': 'F:/dataset/Infrared/NoisySIRST/sirst_random_noise/sirst_random_noise_30/train_rand30/trainval_rand_30/images',
                'description': 'Noisy SIRST dataset with random noise',
                'type': 'direct'
            },
            'NUAA-SIRST': {
                'path': 'F:/dataset/Infrared/NUAA-SIRST/images',
                'description': 'NUAA single-frame infrared small target dataset',
                'type': 'direct'
            },
            'NUDT-SIRST': {
                'path': 'F:/dataset/Infrared/NUDT_SIRST/NUDT/trainval/images',
                'description': 'NUDT infrared small target dataset',
                'type': 'direct'
            },
            'WideIRSTD': {
                'path': 'F:/dataset/Infrared/WideIRSTD-Full Dataset/train/images',
                'description': 'Wide-area infrared small target dataset',
                'type': 'direct'
            }
        }
        
        # Initialize feature extractor
        if TORCH_AVAILABLE:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            self.feature_extractor = SimpleFeatureExtractor().to(self.device)
            self.feature_extractor.eval()
            self.transform = transforms.Compose([
                transforms.Grayscale(),
                transforms.Resize((256, 256)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.5], std=[0.5])
            ])
        
        self.log(f"Initialized Cross-Dataset Domain Analyzer")
        self.log(f"Device: {self.device if TORCH_AVAILABLE else 'CPU (traditional features)'}")
        self.log(f"Max images per dataset: {max_images_per_dataset}")
    
    def log(self, message: str, level: str = "INFO"):
        """Enhanced logging with timestamps"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        icons = {"INFO": "ℹ️", "SUCCESS": "✅", "WARNING": "⚠️", "ERROR": "❌"}
        icon = icons.get(level, "📝")
        print(f"[{timestamp}] {icon} {message}")
    
    def scan_dataset_images(self, dataset_name: str, dataset_path: str, dataset_type: str) -> List[Path]:
        """Scan and collect image files from dataset"""
        path = Path(dataset_path)
        
        if not path.exists():
            self.log(f"Dataset path not found: {dataset_path}", "WARNING")
            return []
        
        image_files = []
        image_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif'}
        
        try:
            if dataset_type == 'direct':
                # Direct image files in the directory
                for ext in image_extensions:
                    image_files.extend(path.glob(f"*{ext}"))
                    image_files.extend(path.glob(f"*{ext.upper()}"))
            
            elif dataset_type == 'nested':
                # Images in subdirectories
                for ext in image_extensions:
                    image_files.extend(path.glob(f"**/*{ext}"))
                    image_files.extend(path.glob(f"**/*{ext.upper()}"))
            
            elif dataset_type == 'processed':
                # Already processed dataset
                for ext in image_extensions:
                    image_files.extend(path.glob(f"*{ext}"))
                    image_files.extend(path.glob(f"*{ext.upper()}"))
            
            # Limit number of images
            if len(image_files) > self.max_images_per_dataset:
                # Sample evenly across the dataset
                step = len(image_files) // self.max_images_per_dataset
                image_files = image_files[::step][:self.max_images_per_dataset]
            
            self.log(f"Found {len(image_files)} images in {dataset_name}")
            return image_files
            
        except Exception as e:
            self.log(f"Error scanning {dataset_name}: {e}", "ERROR")
            return []
    
    def extract_traditional_features(self, image_path: Path) -> Optional[np.ndarray]:
        """Extract traditional image features (when PyTorch is not available)"""
        try:
            # Read image
            img = cv2.imread(str(image_path), cv2.IMREAD_GRAYSCALE)
            if img is None:
                return None
            
            # Resize to standard size
            img = cv2.resize(img, (256, 256))
            
            # Extract statistical features
            features = []
            
            # Basic statistics
            features.extend([
                np.mean(img),
                np.std(img),
                np.min(img),
                np.max(img),
                np.median(img)
            ])
            
            # Histogram features
            hist = cv2.calcHist([img], [0], None, [32], [0, 256])
            features.extend(hist.flatten())
            
            # Texture features (LBP-like)
            # Simple gradient features
            grad_x = cv2.Sobel(img, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(img, cv2.CV_64F, 0, 1, ksize=3)
            
            features.extend([
                np.mean(np.abs(grad_x)),
                np.mean(np.abs(grad_y)),
                np.std(grad_x),
                np.std(grad_y)
            ])
            
            return np.array(features)
            
        except Exception as e:
            return None
    
    def extract_deep_features(self, image_path: Path) -> Optional[np.ndarray]:
        """Extract deep learning features using CNN"""
        try:
            # Load and preprocess image
            img = Image.open(image_path).convert('RGB')
            img_tensor = self.transform(img).unsqueeze(0).to(self.device)
            
            # Extract features
            with torch.no_grad():
                features = self.feature_extractor(img_tensor)
                return features.cpu().numpy().flatten()
                
        except Exception as e:
            return None

    def analyze_dataset_features(self, dataset_name: str) -> Optional[np.ndarray]:
        """Analyze features for a single dataset"""
        config = self.dataset_configs[dataset_name]
        self.log(f"Analyzing {dataset_name}...")

        # Scan images
        image_files = self.scan_dataset_images(
            dataset_name,
            config['path'],
            config['type']
        )

        if not image_files:
            self.log(f"No images found for {dataset_name}", "WARNING")
            return None

        # Extract features
        features_list = []

        if TQDM_AVAILABLE:
            iterator = tqdm(image_files, desc=f"Processing {dataset_name}")
        else:
            iterator = image_files
            self.log(f"Processing {len(image_files)} images...")

        for i, img_path in enumerate(iterator):
            if TORCH_AVAILABLE:
                features = self.extract_deep_features(img_path)
            else:
                features = self.extract_traditional_features(img_path)

            if features is not None:
                features_list.append(features)

            # Progress update for non-tqdm
            if not TQDM_AVAILABLE and (i + 1) % 10 == 0:
                progress = (i + 1) / len(image_files) * 100
                print(f"\rProgress: {progress:.1f}% ({i + 1}/{len(image_files)})", end="")

        if not TQDM_AVAILABLE:
            print()  # New line

        if not features_list:
            self.log(f"No valid features extracted for {dataset_name}", "WARNING")
            return None

        # Convert to numpy array and compute mean features
        features_array = np.array(features_list)
        mean_features = np.mean(features_array, axis=0)

        self.log(f"✓ {dataset_name}: {len(features_list)} images processed", "SUCCESS")
        return mean_features

    def analyze_all_datasets(self) -> Dict[str, np.ndarray]:
        """Analyze features for all datasets"""
        self.log("Starting cross-dataset domain analysis...")

        dataset_features = {}

        for dataset_name in self.dataset_configs.keys():
            features = self.analyze_dataset_features(dataset_name)
            if features is not None:
                dataset_features[dataset_name] = features

        self.log(f"Successfully analyzed {len(dataset_features)} datasets", "SUCCESS")
        return dataset_features

    def compute_similarity_matrix(self, dataset_features: Dict[str, np.ndarray]) -> Tuple[np.ndarray, List[str]]:
        """Compute similarity matrix between datasets"""
        dataset_names = list(dataset_features.keys())
        n_datasets = len(dataset_names)

        if not SKLEARN_AVAILABLE:
            # Simple correlation-based similarity
            similarity_matrix = np.zeros((n_datasets, n_datasets))
            features_list = [dataset_features[name] for name in dataset_names]

            for i in range(n_datasets):
                for j in range(n_datasets):
                    if i == j:
                        similarity_matrix[i, j] = 1.0
                    else:
                        # Normalized correlation
                        corr = np.corrcoef(features_list[i], features_list[j])[0, 1]
                        similarity_matrix[i, j] = max(0, corr)  # Ensure non-negative
        else:
            # Cosine similarity
            features_matrix = np.array([dataset_features[name] for name in dataset_names])
            similarity_matrix = cosine_similarity(features_matrix)

        return similarity_matrix, dataset_names

    def create_similarity_heatmap(self, similarity_matrix: np.ndarray, dataset_names: List[str]):
        """Create similarity heatmap visualization"""
        plt.figure(figsize=(12, 10))

        # Create heatmap
        sns.heatmap(
            similarity_matrix,
            annot=True,
            fmt='.3f',
            cmap='RdYlBu_r',
            square=True,
            xticklabels=dataset_names,
            yticklabels=dataset_names,
            cbar_kws={'label': 'Similarity Score'}
        )

        plt.title('Cross-Dataset Domain Similarity Matrix', fontsize=16, fontweight='bold')
        plt.xlabel('Datasets', fontsize=12)
        plt.ylabel('Datasets', fontsize=12)
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        plt.tight_layout()

        # Save plot
        output_file = self.output_dir / "cross_dataset_similarity_matrix.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()

        self.log(f"✓ Similarity heatmap saved: {output_file}", "SUCCESS")

    def create_pca_visualization(self, dataset_features: Dict[str, np.ndarray]):
        """Create PCA visualization of dataset relationships"""
        if not SKLEARN_AVAILABLE:
            self.log("PCA visualization requires scikit-learn", "WARNING")
            return

        # Prepare data
        dataset_names = list(dataset_features.keys())
        features_matrix = np.array([dataset_features[name] for name in dataset_names])

        # Apply PCA
        pca = PCA(n_components=2)
        pca_features = pca.fit_transform(features_matrix)

        # Create visualization
        plt.figure(figsize=(12, 8))

        # Color map for datasets
        colors = plt.cm.Set3(np.linspace(0, 1, len(dataset_names)))

        for i, (name, color) in enumerate(zip(dataset_names, colors)):
            plt.scatter(pca_features[i, 0], pca_features[i, 1],
                       c=[color], s=200, alpha=0.7, edgecolors='black', linewidth=2)
            plt.annotate(name, (pca_features[i, 0], pca_features[i, 1]),
                        xytext=(5, 5), textcoords='offset points', fontsize=10, fontweight='bold')

        plt.xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.1%} variance)', fontsize=12)
        plt.ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.1%} variance)', fontsize=12)
        plt.title('Cross-Dataset Domain Relationships (PCA)', fontsize=16, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        # Save plot
        output_file = self.output_dir / "cross_dataset_pca_visualization.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()

        self.log(f"✓ PCA visualization saved: {output_file}", "SUCCESS")

    def create_clustering_analysis(self, dataset_features: Dict[str, np.ndarray]):
        """Perform clustering analysis on datasets"""
        if not SKLEARN_AVAILABLE:
            self.log("Clustering analysis requires scikit-learn", "WARNING")
            return None

        dataset_names = list(dataset_features.keys())
        features_matrix = np.array([dataset_features[name] for name in dataset_names])

        # Determine optimal number of clusters (2-4 for small number of datasets)
        max_clusters = min(4, len(dataset_names) - 1)
        best_k = 2

        if len(dataset_names) > 3:
            # Simple elbow method
            inertias = []
            k_range = range(2, max_clusters + 1)

            for k in k_range:
                kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
                kmeans.fit(features_matrix)
                inertias.append(kmeans.inertia_)

            # Find elbow (simple method)
            if len(inertias) > 1:
                diffs = np.diff(inertias)
                best_k = k_range[np.argmin(diffs)] if len(diffs) > 0 else 2

        # Perform clustering
        kmeans = KMeans(n_clusters=best_k, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(features_matrix)

        # Create clustering results
        clustering_results = {}
        for i, dataset_name in enumerate(dataset_names):
            clustering_results[dataset_name] = {
                'cluster': int(cluster_labels[i]),
                'features': dataset_features[dataset_name].tolist()
            }

        self.log(f"✓ Clustering completed: {best_k} clusters identified", "SUCCESS")
        return clustering_results, best_k

    def generate_comprehensive_report(self, dataset_features: Dict[str, np.ndarray]):
        """Generate comprehensive cross-dataset analysis report"""
        self.log("Generating comprehensive analysis report...")

        # Compute similarity matrix
        similarity_matrix, dataset_names = self.compute_similarity_matrix(dataset_features)

        # Create visualizations
        self.create_similarity_heatmap(similarity_matrix, dataset_names)
        self.create_pca_visualization(dataset_features)

        # Clustering analysis
        clustering_results = None
        if SKLEARN_AVAILABLE:
            clustering_results, n_clusters = self.create_clustering_analysis(dataset_features)

        # Generate summary report
        report = {
            'analysis_timestamp': datetime.now().isoformat(),
            'datasets_analyzed': len(dataset_features),
            'dataset_info': {},
            'similarity_analysis': {},
            'clustering_analysis': clustering_results,
            'recommendations': []
        }

        # Add dataset information
        for name in dataset_names:
            config = self.dataset_configs[name]
            report['dataset_info'][name] = {
                'description': config['description'],
                'path': config['path'],
                'type': config['type'],
                'feature_dimension': len(dataset_features[name])
            }

        # Add similarity analysis
        for i, name1 in enumerate(dataset_names):
            for j, name2 in enumerate(dataset_names):
                if i < j:  # Only upper triangle
                    similarity = similarity_matrix[i, j]
                    pair_key = f"{name1}_vs_{name2}"
                    report['similarity_analysis'][pair_key] = {
                        'similarity_score': float(similarity),
                        'relationship': self.interpret_similarity(similarity)
                    }

        # Add recommendations
        report['recommendations'] = self.generate_recommendations(similarity_matrix, dataset_names)

        # Save report
        report_file = self.output_dir / "cross_dataset_analysis_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        self.log(f"✓ Comprehensive report saved: {report_file}", "SUCCESS")
        return report

    def interpret_similarity(self, similarity: float) -> str:
        """Interpret similarity score"""
        if similarity > 0.8:
            return "Very High Similarity"
        elif similarity > 0.6:
            return "High Similarity"
        elif similarity > 0.4:
            return "Moderate Similarity"
        elif similarity > 0.2:
            return "Low Similarity"
        else:
            return "Very Low Similarity"

    def generate_recommendations(self, similarity_matrix: np.ndarray, dataset_names: List[str]) -> List[str]:
        """Generate recommendations based on analysis"""
        recommendations = []

        # Find most similar datasets to Sim2Real_30k
        if 'Sim2Real_30k' in dataset_names:
            sim2real_idx = dataset_names.index('Sim2Real_30k')
            similarities = similarity_matrix[sim2real_idx]

            # Sort by similarity (excluding self)
            other_indices = [i for i in range(len(dataset_names)) if i != sim2real_idx]
            sorted_indices = sorted(other_indices, key=lambda i: similarities[i], reverse=True)

            most_similar = dataset_names[sorted_indices[0]]
            least_similar = dataset_names[sorted_indices[-1]]

            recommendations.extend([
                f"Most similar dataset to Sim2Real_30k: {most_similar} (similarity: {similarities[sorted_indices[0]]:.3f})",
                f"Least similar dataset to Sim2Real_30k: {least_similar} (similarity: {similarities[sorted_indices[-1]]:.3f})",
                f"For domain adaptation, consider using {most_similar} as source domain",
                f"For robustness testing, use {least_similar} to evaluate generalization"
            ])

        # General recommendations
        avg_similarity = np.mean(similarity_matrix[np.triu_indices_from(similarity_matrix, k=1)])

        if avg_similarity > 0.7:
            recommendations.append("High overall similarity detected - datasets may have limited diversity")
        elif avg_similarity < 0.3:
            recommendations.append("Low overall similarity detected - significant domain gaps exist")
        else:
            recommendations.append("Moderate similarity detected - good balance of diversity and consistency")

        recommendations.extend([
            "Consider ensemble methods to leverage multiple datasets",
            "Implement progressive training starting from most similar datasets",
            "Use domain adaptation techniques for cross-dataset generalization"
        ])

        return recommendations


def main():
    parser = argparse.ArgumentParser(description='Cross-Dataset Domain Gap Analysis')
    parser.add_argument('-o', '--output', default='cross_dataset_analysis', help='Output directory')
    parser.add_argument('--max-images', type=int, default=100, help='Maximum images per dataset')
    parser.add_argument('--datasets', nargs='+', help='Specific datasets to analyze (optional)')

    args = parser.parse_args()

    try:
        # Create analyzer
        analyzer = CrossDatasetDomainAnalyzer(args.output, args.max_images)

        # Filter datasets if specified
        if args.datasets:
            available_datasets = set(analyzer.dataset_configs.keys())
            requested_datasets = set(args.datasets)
            invalid_datasets = requested_datasets - available_datasets

            if invalid_datasets:
                print(f"⚠️ Invalid datasets: {invalid_datasets}")
                print(f"Available datasets: {available_datasets}")
                return 1

            # Filter configurations
            filtered_configs = {k: v for k, v in analyzer.dataset_configs.items() if k in requested_datasets}
            analyzer.dataset_configs = filtered_configs

        # Analyze all datasets
        dataset_features = analyzer.analyze_all_datasets()

        if len(dataset_features) < 2:
            print("❌ Need at least 2 datasets for comparison analysis")
            return 1

        # Generate comprehensive report
        report = analyzer.generate_comprehensive_report(dataset_features)

        print(f"\n🎉 Cross-dataset domain analysis completed!")
        print(f"📁 Output directory: {args.output}")
        print(f"📊 Datasets analyzed: {len(dataset_features)}")
        print(f"📈 Generated files:")
        print(f"  - cross_dataset_similarity_matrix.png")
        print(f"  - cross_dataset_pca_visualization.png")
        print(f"  - cross_dataset_analysis_report.json")

        return 0

    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())
